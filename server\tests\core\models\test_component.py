#!/usr/bin/env python3
"""Unit tests for Component model.

This module contains comprehensive unit tests for the Component database model,
testing all functionality including validation, relationships, and business logic.
"""

import json
import pytest
from decimal import Decimal
from datetime import datetime
from unittest.mock import Mock

from sqlalchemy.exc import IntegrityError

from src.core.models.general.component import Component
from src.core.models.general.component_type import ComponentType
from src.core.models.general.component_category import ComponentCategory


class TestComponentModel:
    """Test suite for Component model functionality."""

    def create_test_component_type_and_category(
        self,
        db_session,
        type_name="Circuit Breaker",
        category_name="Protection Devices",
    ):
        """Helper method to create ComponentType and ComponentCategory for testing."""
        category = ComponentCategory(
            name=category_name, description=f"{category_name} category", is_active=True
        )
        db_session.add(category)
        db_session.flush()

        component_type = ComponentType(
            name=type_name,
            description=f"{type_name} type",
            category_id=category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        return component_type, category

    def test_component_creation_basic(self, db_session):
        """Test basic component creation with required fields."""
        component_type, category = self.create_test_component_type_and_category(
            db_session
        )

        component = Component(
            name="Test Circuit Breaker",
            manufacturer="ABB",
            model_number="S203-B16",
            component_type_id=component_type.id,
            component_category_id=category.id,
        )

        db_session.add(component)
        db_session.commit()

        assert component.id is not None
        assert component.name == "Test Circuit Breaker"
        assert component.manufacturer == "ABB"
        assert component.model_number == "S203-B16"
        assert component.component_type_id == component_type.id
        assert component.component_category_id == category.id
        assert component.is_active is True
        assert component.is_preferred is False
        assert component.currency == "USD"
        assert component.stock_status == "available"
        assert component.version == "1.0"

    def test_component_auto_category_assignment(self, db_session):
        """Test relational approach - component type belongs to category."""
        # Create necessary ComponentCategory and ComponentType entries
        category = ComponentCategory(
            name="Loads", description="Electrical loads", is_active=True
        )
        db_session.add(category)
        db_session.flush()

        component_type = ComponentType(
            name="Electric Motor",
            description="Electric motor load",
            category_id=category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        component = Component(
            name="Test Motor",
            manufacturer="Siemens",
            model_number="1LA7090-4AA60",
            component_type_id=component_type.id,
            component_category_id=category.id,
        )

        db_session.add(component)
        db_session.commit()

        assert component.component_type_entity.category_id == category.id

    def test_component_category_validation(self, db_session):
        """Test validation of category matching component type."""
        # Create categories and types that don't match
        load_category = ComponentCategory(
            name="Loads", description="Electrical loads", is_active=True
        )
        protection_category = ComponentCategory(
            name="Protection Devices",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add_all([load_category, protection_category])
        db_session.flush()

        motor_type = ComponentType(
            name="Electric Motor",
            description="Electric motor load",
            category_id=load_category.id,
            is_active=True,
        )
        db_session.add(motor_type)
        db_session.flush()

        # This should work fine as we validate category consistency through the model
        component = Component(
            name="Test Motor",
            manufacturer="Siemens",
            model_number="1LA7090-4AA60",
            component_type_id=motor_type.id,
            component_category_id=load_category.id,  # Use the correct category
        )

        db_session.add(component)
        db_session.commit()

        # Verify the component is valid
        assert component.is_category_valid() is True

    def test_component_with_specifications(self, db_session):
        """Test component creation with electrical specifications."""
        specifications = {
            "electrical": {
                "voltage_rating": {"value": 400, "unit": "V"},
                "current_rating": {"value": 16, "unit": "A"},
                "power_rating": {"value": 3000, "unit": "W"},
            },
            "mechanical": {"mounting": "DIN rail", "protection_class": "IP20"},
            "standards_compliance": ["IEC 60947-2", "EN 60947-2"],
        }

        # Create necessary ComponentCategory and ComponentType entries
        category = ComponentCategory(
            name="Protection Devices",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(category)
        db_session.flush()

        component_type = ComponentType(
            name="Circuit Breaker",
            description="Circuit breaker protection device",
            category_id=category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        component = Component(
            name="Circuit Breaker with Specs",
            manufacturer="Schneider Electric",
            model_number="C60N-B16",
            component_type_id=component_type.id,
            component_category_id=category.id,
            specifications=json.dumps(specifications),
            unit_price=Decimal("45.50"),
            currency="EUR",
        )

        db_session.add(component)
        db_session.commit()

        assert component.specifications is not None
        assert component.unit_price == Decimal("45.50")
        assert component.currency == "EUR"

    def test_component_with_physical_properties(self, db_session):
        """Test component with physical dimensions and weight."""
        dimensions = {
            "length": {"value": 85, "unit": "mm"},
            "width": {"value": 18, "unit": "mm"},
            "height": {"value": 78, "unit": "mm"},
        }

        component = Component(
            name="Compact Circuit Breaker",
            manufacturer="ABB",
            model_number="S201-B10",
            component_type=ComponentType.CIRCUIT_BREAKER,
            weight_kg=0.125,
            dimensions_json=json.dumps(dimensions),
        )

        db_session.add(component)
        db_session.commit()

        assert component.weight_kg == 0.125
        assert component.dimensions_json is not None

    def test_component_unique_constraint(self, db_session):
        """Test unique constraint on manufacturer + model_number."""
        # Create first component
        component1 = Component(
            name="First Component",
            manufacturer="ABB",
            model_number="S203-B16",
            component_type=ComponentType.CIRCUIT_BREAKER,
        )
        db_session.add(component1)
        db_session.commit()

        # Try to create duplicate
        component2 = Component(
            name="Second Component",
            manufacturer="ABB",
            model_number="S203-B16",  # Same manufacturer + model
            component_type=ComponentType.CIRCUIT_BREAKER,
        )
        db_session.add(component2)

        with pytest.raises(IntegrityError):
            db_session.commit()

    def test_component_soft_delete(self, db_session):
        """Test soft delete functionality."""
        component = Component(
            name="Test Component",
            manufacturer="Test Manufacturer",
            model_number="TEST-001",
            component_type=ComponentType.CIRCUIT_BREAKER,
        )

        db_session.add(component)
        db_session.commit()

        # Soft delete
        component.is_deleted = True
        component.deleted_at = datetime.utcnow()
        db_session.commit()

        assert component.is_deleted is True
        assert component.deleted_at is not None

    def test_component_string_representations(self, db_session):
        """Test string representation methods."""
        component = Component(
            name="Test Circuit Breaker",
            manufacturer="ABB",
            model_number="S203-B16",
            component_type=ComponentType.CIRCUIT_BREAKER,
        )

        db_session.add(component)
        db_session.commit()

        # Test __str__
        str_repr = str(component)
        assert "ABB" in str_repr
        assert "S203-B16" in str_repr
        assert "Circuit Breaker" in str_repr

        # Test __repr__
        repr_str = repr(component)
        assert "Component" in repr_str
        assert "ABB" in repr_str
        assert "S203-B16" in repr_str

    def test_component_properties(self, db_session):
        """Test component property methods."""
        component = Component(
            name="Custom Name",
            manufacturer="ABB",
            model_number="S203-B16",
            component_type=ComponentType.CIRCUIT_BREAKER,
        )

        db_session.add(component)
        db_session.commit()

        # Test full_name property
        assert component.full_name == "ABB S203-B16"

        # Test display_name property
        display_name = component.display_name
        assert "Custom Name" in display_name
        assert "ABB S203-B16" in display_name

    def test_is_category_valid(self, db_session):
        """Test category validation method."""
        component = Component(
            name="Test Motor",
            manufacturer="Siemens",
            model_number="1LA7090-4AA60",
            component_type=ComponentType.ELECTRIC_MOTOR,
        )

        db_session.add(component)
        db_session.commit()

        assert component.is_category_valid() is True

    def test_specification_value_methods(self, db_session):
        """Test specification getter and setter methods."""
        component = Component(
            name="Test Component",
            manufacturer="Test Manufacturer",
            model_number="TEST-001",
            component_type=ComponentType.CIRCUIT_BREAKER,
        )

        db_session.add(component)
        db_session.commit()

        # Test setting specification value
        component.set_specification_value("voltage_rating", {"value": 230, "unit": "V"})
        db_session.commit()

        # Test getting specification value
        voltage_rating = component.get_specification_value("voltage_rating")
        assert voltage_rating == {"value": 230, "unit": "V"}

        # Test getting non-existent value with default
        non_existent = component.get_specification_value("non_existent", "default")
        assert non_existent == "default"

    def test_get_electrical_rating(self, db_session):
        """Test electrical rating retrieval method."""
        specifications = {
            "electrical": {
                "voltage_rating": {"value": 400, "unit": "V"},
                "current_rating": {"value": 16, "unit": "A"},
            }
        }

        component = Component(
            name="Test Component",
            manufacturer="Test Manufacturer",
            model_number="TEST-001",
            component_type=ComponentType.CIRCUIT_BREAKER,
            specifications=json.dumps(specifications),
        )

        db_session.add(component)
        db_session.commit()

        voltage_rating = component.get_electrical_rating("voltage")
        assert voltage_rating == {"value": 400, "unit": "V"}

        # Test non-existent rating
        power_rating = component.get_electrical_rating("power")
        assert power_rating is None

    def test_is_compatible_with_standards(self, db_session):
        """Test standards compliance checking."""
        specifications = {
            "standards_compliance": ["IEC 60947-2", "EN 60947-2", "UL 489"]
        }

        component = Component(
            name="Test Component",
            manufacturer="Test Manufacturer",
            model_number="TEST-001",
            component_type=ComponentType.CIRCUIT_BREAKER,
            specifications=json.dumps(specifications),
        )

        db_session.add(component)
        db_session.commit()

        # Test compatible standards
        assert component.is_compatible_with_standards(["IEC 60947-2"]) is True
        assert (
            component.is_compatible_with_standards(["IEC 60947-2", "EN 60947-2"])
            is True
        )

        # Test incompatible standards
        assert (
            component.is_compatible_with_standards(["IEC 60947-2", "UNKNOWN"]) is False
        )
        assert component.is_compatible_with_standards(["UNKNOWN"]) is False

    def test_calculate_total_cost(self, db_session):
        """Test total cost calculation method."""
        component = Component(
            name="Test Component",
            manufacturer="Test Manufacturer",
            model_number="TEST-001",
            component_type=ComponentType.CIRCUIT_BREAKER,
            unit_price=Decimal("25.50"),
        )

        db_session.add(component)
        db_session.commit()

        # Test basic calculation
        total = component.calculate_total_cost(quantity=2)
        assert total == Decimal("51.00")

        # Test with tax
        total_with_tax = component.calculate_total_cost(
            quantity=2, include_tax=True, tax_rate=0.08
        )
        expected = Decimal("51.00") * Decimal("1.08")
        assert total_with_tax == expected.quantize(Decimal("0.01"))

        # Test with no unit price
        component.unit_price = None
        db_session.commit()

        total_no_price = component.calculate_total_cost(quantity=2)
        assert total_no_price is None

    def test_component_with_supplier_info(self, db_session):
        """Test component with supplier and part number information."""
        component = Component(
            name="Industrial Circuit Breaker",
            manufacturer="ABB",
            model_number="S203-B16",
            component_type=ComponentType.CIRCUIT_BREAKER,
            supplier="RS Components",
            part_number="123-4567",
            unit_price=Decimal("42.75"),
            currency="GBP",
        )

        db_session.add(component)
        db_session.commit()

        assert component.supplier == "RS Components"
        assert component.part_number == "123-4567"
        assert component.unit_price == Decimal("42.75")
        assert component.currency == "GBP"

    def test_component_status_fields(self, db_session):
        """Test component status and preference fields."""
        component = Component(
            name="Preferred Component",
            manufacturer="Siemens",
            model_number="3RT1015-1BB41",
            component_type=ComponentType.CONTACTOR,
            is_active=True,
            is_preferred=True,
            stock_status="in_stock",
        )

        db_session.add(component)
        db_session.commit()

        assert component.is_active is True
        assert component.is_preferred is True
        assert component.stock_status == "in_stock"

    def test_component_version_and_metadata(self, db_session):
        """Test component version control and metadata fields."""
        metadata = {
            "import_source": "supplier_catalog",
            "last_updated": "2024-01-15",
            "quality_rating": 5,
            "notes": "High reliability component",
        }

        component = Component(
            name="Versioned Component",
            manufacturer="Phoenix Contact",
            model_number="REL-MR-24DC/21",
            component_type=ComponentType.CONTROL_RELAY,
            version="2.1",
            metadata_json=json.dumps(metadata),
        )

        db_session.add(component)
        db_session.commit()

        assert component.version == "2.1"
        assert component.metadata_json is not None

    def test_component_with_invalid_json_specifications(self, db_session):
        """Test component behavior with invalid JSON in specifications."""
        component = Component(
            name="Test Component",
            manufacturer="Test Manufacturer",
            model_number="TEST-001",
            component_type=ComponentType.CIRCUIT_BREAKER,
            specifications="invalid json string",
        )

        db_session.add(component)
        db_session.commit()

        # Should handle invalid JSON gracefully
        value = component.get_specification_value("test_key", "default")
        assert value == "default"

        # Setting value should create valid JSON
        component.set_specification_value("new_key", "new_value")
        db_session.commit()

        retrieved_value = component.get_specification_value("new_key")
        assert retrieved_value == "new_value"
