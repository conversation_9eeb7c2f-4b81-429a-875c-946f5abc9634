#!/usr/bin/env python3
"""Component Type Schemas for Ultimate Electrical Designer.

This module provides comprehensive Pydantic schemas for component type management
operations, supporting CRUD operations, validation, and serialization for
electrical component type classification.

Key Features:
- Comprehensive field validation with custom validators
- Category relationship validation and integrity
- Input sanitization and data transformation
- Output formatting and serialization rules
- Schema versioning support and backward compatibility
- Custom validation rules for electrical component standards
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator, model_validator

from src.core.schemas.base import BaseSchema, TimestampMixin


class ComponentTypeBaseSchema(BaseSchema):
    """Base schema for component type operations."""

    name: str = Field(
        ..., min_length=1, max_length=100, description="Component type name"
    )
    description: Optional[str] = Field(
        None, max_length=1000, description="Detailed component type description"
    )
    category_id: int = Field(..., ge=1, description="Component category ID")
    is_active: bool = Field(
        True, description="Whether component type is active in the system"
    )
    specifications_template: Optional[Dict[str, Any]] = Field(
        None, description="JSON template for component specifications"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata for component type"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate and sanitize component type name."""
        if not v or not v.strip():
            raise ValueError("Component type name cannot be empty")

        # Sanitize the name
        sanitized = v.strip()

        # Check for invalid characters
        invalid_chars = ["<", ">", '"', "'", "&", "\n", "\r", "\t"]
        for char in invalid_chars:
            if char in sanitized:
                raise ValueError(f"Component type name cannot contain '{char}'")

        return sanitized

    @field_validator("description")
    @classmethod
    def validate_description(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize description."""
        if v is None:
            return v

        # Sanitize the description
        sanitized = v.strip()
        if not sanitized:
            return None

        # Check for invalid characters
        invalid_chars = ["<", ">", '"', "'", "&"]
        for char in invalid_chars:
            if char in sanitized:
                raise ValueError(f"Description cannot contain '{char}'")

        return sanitized

    @field_validator("specifications_template")
    @classmethod
    def validate_specifications_template(
        cls, v: Optional[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Validate specifications template structure."""
        if v is None:
            return v

        if not isinstance(v, dict):
            raise ValueError("Specifications template must be a dictionary")

        # Basic validation of template structure
        allowed_sections = [
            "electrical",
            "thermal",
            "mechanical",
            "environmental",
            "standards_compliance",
            "physical",
            "performance",
        ]

        for section in v.keys():
            if section not in allowed_sections:
                raise ValueError(f"Invalid specification section: {section}")

        return v

    @field_validator("metadata")
    @classmethod
    def validate_metadata(cls, v: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Validate metadata structure."""
        if v is None:
            return v

        if not isinstance(v, dict):
            raise ValueError("Metadata must be a dictionary")

        return v


class ComponentTypeCreateSchema(ComponentTypeBaseSchema):
    """Schema for creating component types."""

    @model_validator(mode="after")
    def validate_component_type_creation(self) -> "ComponentTypeCreateSchema":
        """Validate component type creation data."""
        # Additional validation can be added here
        return self


class ComponentTypeUpdateSchema(BaseModel):
    """Schema for updating component types with partial validation."""

    name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="Component type name"
    )
    description: Optional[str] = Field(
        None, max_length=1000, description="Detailed component type description"
    )
    category_id: Optional[int] = Field(None, ge=1, description="Component category ID")
    is_active: Optional[bool] = Field(
        None, description="Whether component type is active in the system"
    )
    specifications_template: Optional[Dict[str, Any]] = Field(
        None, description="JSON template for component specifications"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata for component type"
    )

    # Apply same validators as base schema
    @field_validator("name")
    @classmethod
    def validate_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize component type name."""
        if v is None:
            return v
        return ComponentTypeBaseSchema.validate_name(v)

    @field_validator("description")
    @classmethod
    def validate_description(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize description."""
        return ComponentTypeBaseSchema.validate_description(v)

    @field_validator("specifications_template")
    @classmethod
    def validate_specifications_template(
        cls, v: Optional[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Validate specifications template structure."""
        return ComponentTypeBaseSchema.validate_specifications_template(v)

    @field_validator("metadata")
    @classmethod
    def validate_metadata(cls, v: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Validate metadata structure."""
        return ComponentTypeBaseSchema.validate_metadata(v)


class ComponentTypeReadSchema(ComponentTypeBaseSchema, TimestampMixin):
    """Schema for reading component types."""

    id: int = Field(..., description="Component type ID")
    full_name: str = Field(..., description="Full name including category")
    category_path: str = Field(..., description="Full category path")
    component_count: int = Field(..., description="Number of components of this type")
    has_specifications_template: bool = Field(
        ..., description="Whether type has specifications template"
    )

    # Category information
    category_name: Optional[str] = Field(None, description="Category name")

    class Config:
        """Pydantic configuration for ComponentTypeReadSchema."""

        from_attributes = True


class ComponentTypeSummarySchema(BaseModel):
    """Schema for component type summary information."""

    id: int = Field(..., description="Component type ID")
    name: str = Field(..., description="Component type name")
    description: Optional[str] = Field(None, description="Component type description")
    category_id: int = Field(..., description="Category ID")
    category_name: Optional[str] = Field(None, description="Category name")
    is_active: bool = Field(..., description="Whether component type is active")
    component_count: int = Field(..., description="Number of components")

    class Config:
        """Pydantic configuration for ComponentTypeSummarySchema."""

        from_attributes = True


class ComponentTypeListResponseSchema(BaseModel):
    """Schema for paginated component type list responses."""

    component_types: List[ComponentTypeSummarySchema] = Field(
        ..., description="List of component types"
    )
    total_count: int = Field(..., description="Total number of component types")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_previous: bool = Field(..., description="Whether there are previous pages")


class ComponentTypeSearchSchema(BaseModel):
    """Schema for component type search parameters."""

    search_term: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="Search term for name or description",
    )
    category_id: Optional[int] = Field(None, ge=1, description="Filter by category")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    has_specifications_template: Optional[bool] = Field(
        None, description="Filter by presence of specifications template"
    )
    min_component_count: Optional[int] = Field(
        None, ge=0, description="Minimum number of components"
    )
    max_component_count: Optional[int] = Field(
        None, ge=0, description="Maximum number of components"
    )


class ComponentTypeBulkCreateSchema(BaseModel):
    """Schema for bulk component type creation."""

    component_types: List[ComponentTypeCreateSchema] = Field(
        ...,
        min_length=1,
        max_length=100,
        description="List of component types to create",
    )
    validate_categories: bool = Field(
        True, description="Whether to validate category relationships"
    )
    skip_duplicates: bool = Field(
        False, description="Whether to skip duplicate component types"
    )


class ComponentTypeBulkUpdateSchema(BaseModel):
    """Schema for bulk component type updates."""

    type_updates: List[Dict[str, Any]] = Field(
        ...,
        min_length=1,
        max_length=100,
        description="List of component type updates with ID and fields",
    )
    validate_categories: bool = Field(
        True, description="Whether to validate category relationships"
    )


class ComponentTypeValidationResultSchema(BaseModel):
    """Schema for component type validation results."""

    is_valid: bool = Field(..., description="Whether validation passed")
    errors: List[str] = Field(default_factory=list, description="Validation errors")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    component_type_id: Optional[int] = Field(
        None, description="Component type ID if applicable"
    )


class ComponentTypeStatsSchema(BaseModel):
    """Schema for component type statistics."""

    total_types: int = Field(..., description="Total number of component types")
    active_types: int = Field(..., description="Number of active component types")
    types_by_category: Dict[str, int] = Field(
        ..., description="Component types grouped by category"
    )
    types_with_templates: int = Field(
        ..., description="Types with specifications templates"
    )
    avg_components_per_type: float = Field(
        ..., description="Average components per type"
    )
    types_with_no_components: int = Field(..., description="Types with no components")


class ComponentTypeSpecificationTemplateSchema(BaseModel):
    """Schema for component type specification templates."""

    electrical: Optional[Dict[str, Any]] = Field(
        None, description="Electrical specifications template"
    )
    thermal: Optional[Dict[str, Any]] = Field(
        None, description="Thermal specifications template"
    )
    mechanical: Optional[Dict[str, Any]] = Field(
        None, description="Mechanical specifications template"
    )
    environmental: Optional[Dict[str, Any]] = Field(
        None, description="Environmental specifications template"
    )
    standards_compliance: Optional[List[str]] = Field(
        None, description="Applicable standards template"
    )
    physical: Optional[Dict[str, Any]] = Field(
        None, description="Physical specifications template"
    )
    performance: Optional[Dict[str, Any]] = Field(
        None, description="Performance specifications template"
    )


__all__ = [
    "ComponentTypeBaseSchema",
    "ComponentTypeCreateSchema",
    "ComponentTypeUpdateSchema",
    "ComponentTypeReadSchema",
    "ComponentTypeSummarySchema",
    "ComponentTypeListResponseSchema",
    "ComponentTypeSearchSchema",
    "ComponentTypeBulkCreateSchema",
    "ComponentTypeBulkUpdateSchema",
    "ComponentTypeValidationResultSchema",
    "ComponentTypeStatsSchema",
    "ComponentTypeSpecificationTemplateSchema",
]
