/**
 * Unit tests for useAudit hooks
 */

import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactNode } from 'react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'

import {
  useActivityLogs,
  useCreateActivityLog,
  useUpdateActivityLog,
  useAuditTrails,
  useCreateAuditTrail,
  useRecordHistory,
  useLogUserActivity,
  useLogSecurityEvent,
  useLogDataChange,
  useAuditSummary,
  useUserActivitySummary,
} from '../useAudit'
import { auditApiClient } from '@/lib/api/audit'

// Mock the auditApiClient
vi.mock('@/lib/api/audit', () => ({
  auditApiClient: {
    getActivityLogs: vi.fn(),
    createActivityLog: vi.fn(),
    updateActivityLog: vi.fn(),
    getAuditTrails: vi.fn(),
    createAuditTrail: vi.fn(),
    getRecordHistory: vi.fn(),
    logUserActivity: vi.fn(),
    logSecurityEvent: vi.fn(),
    logDataChange: vi.fn(),
    getAuditSummary: vi.fn(),
    getUserActivitySummary: vi.fn(),
  },
}))

// Mock data
const mockActivityLogs = {
  items: [
    {
      id: 1,
      name: 'LOGIN_2023_01_01',
      user_id: 1,
      session_id: 'session_123',
      action_type: 'LOGIN',
      action_description: 'User logged in successfully',
      target_type: 'User',
      target_id: 1,
      target_name: 'john.doe',
      request_method: 'POST',
      request_path: '/api/v1/auth/login',
      request_ip: '***********',
      user_agent: 'Mozilla/5.0',
      status: 'SUCCESS',
      severity: 'INFO',
      metadata: { login_method: 'password' },
      error_message: null,
      execution_time_ms: 150,
      category: 'AUTHENTICATION',
      tags: ['auth', 'login'],
      is_security_related: true,
      is_data_change: false,
      is_system_event: false,
      notes: 'Regular login',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
  ],
  total: 1,
  page: 1,
  size: 10,
  pages: 1,
}

const mockAuditTrails = {
  items: [
    {
      id: 1,
      name: 'User_1_UPDATE_2023_01_01',
      activity_log_id: 1,
      user_id: 1,
      changed_at: '2023-01-01T00:00:00Z',
      table_name: 'User',
      record_id: 1,
      operation: 'UPDATE',
      field_name: 'email',
      old_value: '<EMAIL>',
      new_value: '<EMAIL>',
      change_reason: 'User requested email change',
      change_context: { request_source: 'profile_page' },
      is_sensitive: false,
      is_system_change: false,
      notes: 'Email update',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
  ],
  total: 1,
  page: 1,
  size: 10,
  pages: 1,
}

const mockRecordHistory = {
  table_name: 'User',
  record_id: 1,
  total_changes: 3,
  first_change: '2023-01-01T00:00:00Z',
  last_change: '2023-01-03T00:00:00Z',
  changes: [
    {
      id: 1,
      name: 'User_1_UPDATE_2023_01_01',
      activity_log_id: 1,
      user_id: 1,
      changed_at: '2023-01-01T00:00:00Z',
      table_name: 'User',
      record_id: 1,
      operation: 'UPDATE',
      field_name: 'email',
      old_value: '<EMAIL>',
      new_value: '<EMAIL>',
      change_reason: 'User requested email change',
      change_context: { request_source: 'profile_page' },
      is_sensitive: false,
      is_system_change: false,
      notes: 'Email update',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
  ],
  change_summary: {
    UPDATE: 2,
    INSERT: 1,
  },
}

const mockAuditSummary = {
  total_activities: 100,
  total_data_changes: 50,
  security_events: 10,
  system_events: 5,
  user_actions: 85,
  failed_operations: 5,
  top_action_types: [
    { action_type: 'LOGIN', count: 20 },
    { action_type: 'UPDATE', count: 15 },
  ],
  top_users: [
    { user_id: 1, user_name: 'john.doe', count: 25 },
    { user_id: 2, user_name: 'jane.smith', count: 20 },
  ],
  activity_timeline: [
    { date: '2023-01-01', count: 10 },
    { date: '2023-01-02', count: 15 },
  ],
}

const mockUserActivitySummary = {
  user_id: 1,
  total_activities: 25,
  recent_activities: [mockActivityLogs.items[0]],
  top_actions: [
    { action_type: 'LOGIN', count: 10 },
    { action_type: 'UPDATE', count: 8 },
  ],
  security_events: 5,
  data_changes: 12,
  activity_timeline: [
    { date: '2023-01-01', count: 5 },
    { date: '2023-01-02', count: 8 },
  ],
}

// Create a wrapper for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useAudit hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('useActivityLogs', () => {
    it('should fetch activity logs successfully', async () => {
      const mockGetActivityLogs = vi.mocked(auditApiClient.getActivityLogs)
      mockGetActivityLogs.mockResolvedValue(mockActivityLogs)

      const { result } = renderHook(() => useActivityLogs(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockActivityLogs)
      expect(mockGetActivityLogs).toHaveBeenCalledWith(undefined)
    })

    it('should fetch activity logs with filters', async () => {
      const mockGetActivityLogs = vi.mocked(auditApiClient.getActivityLogs)
      mockGetActivityLogs.mockResolvedValue(mockActivityLogs)

      const filters = {
        user_id: 1,
        action_types: ['LOGIN'],
        is_security_related: true,
      }

      const { result } = renderHook(() => useActivityLogs(filters), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockGetActivityLogs).toHaveBeenCalledWith(filters)
    })
  })

  describe('useCreateActivityLog', () => {
    it('should create activity log successfully', async () => {
      const mockCreateActivityLog = vi.mocked(auditApiClient.createActivityLog)
      const newActivityLog = {
        name: 'TEST_LOG',
        action_type: 'TEST',
        action_description: 'Test activity log',
        user_id: 1,
      }

      mockCreateActivityLog.mockResolvedValue({
        ...mockActivityLogs.items[0],
        ...newActivityLog,
      })

      const { result } = renderHook(() => useCreateActivityLog(), {
        wrapper: createWrapper(),
      })

      await result.current.mutateAsync(newActivityLog)

      expect(mockCreateActivityLog).toHaveBeenCalledWith(newActivityLog)
      expect(result.current.isSuccess).toBe(true)
    })
  })

  describe('useUpdateActivityLog', () => {
    it('should update activity log successfully', async () => {
      const mockUpdateActivityLog = vi.mocked(auditApiClient.updateActivityLog)
      const updateData = {
        status: 'COMPLETED',
        execution_time_ms: 200,
      }

      mockUpdateActivityLog.mockResolvedValue({
        ...mockActivityLogs.items[0],
        ...updateData,
      })

      const { result } = renderHook(() => useUpdateActivityLog(), {
        wrapper: createWrapper(),
      })

      await result.current.mutateAsync({ id: 1, data: updateData })

      expect(mockUpdateActivityLog).toHaveBeenCalledWith(1, updateData)
      expect(result.current.isSuccess).toBe(true)
    })
  })

  describe('useAuditTrails', () => {
    it('should fetch audit trails successfully', async () => {
      const mockGetAuditTrails = vi.mocked(auditApiClient.getAuditTrails)
      mockGetAuditTrails.mockResolvedValue(mockAuditTrails)

      const { result } = renderHook(() => useAuditTrails(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockAuditTrails)
      expect(mockGetAuditTrails).toHaveBeenCalledWith(undefined)
    })

    it('should fetch audit trails with filters', async () => {
      const mockGetAuditTrails = vi.mocked(auditApiClient.getAuditTrails)
      mockGetAuditTrails.mockResolvedValue(mockAuditTrails)

      const filters = {
        table_name: 'User',
        record_id: 1,
        operations: ['UPDATE'],
      }

      const { result } = renderHook(() => useAuditTrails(filters), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockGetAuditTrails).toHaveBeenCalledWith(filters)
    })
  })

  describe('useCreateAuditTrail', () => {
    it('should create audit trail successfully', async () => {
      const mockCreateAuditTrail = vi.mocked(auditApiClient.createAuditTrail)
      const newAuditTrail = {
        name: 'TEST_AUDIT',
        table_name: 'User',
        record_id: 1,
        operation: 'UPDATE',
        field_name: 'name',
        old_value: 'old_name',
        new_value: 'new_name',
      }

      mockCreateAuditTrail.mockResolvedValue({
        ...mockAuditTrails.items[0],
        ...newAuditTrail,
      })

      const { result } = renderHook(() => useCreateAuditTrail(), {
        wrapper: createWrapper(),
      })

      await result.current.mutateAsync(newAuditTrail)

      expect(mockCreateAuditTrail).toHaveBeenCalledWith(newAuditTrail)
      expect(result.current.isSuccess).toBe(true)
    })
  })

  describe('useRecordHistory', () => {
    it('should fetch record history successfully', async () => {
      const mockGetRecordHistory = vi.mocked(auditApiClient.getRecordHistory)
      mockGetRecordHistory.mockResolvedValue(mockRecordHistory)

      const { result } = renderHook(() => useRecordHistory('User', 1), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockRecordHistory)
      expect(mockGetRecordHistory).toHaveBeenCalledWith('User', 1, undefined)
    })

    it('should not fetch when tableName or recordId is missing', () => {
      const mockGetRecordHistory = vi.mocked(auditApiClient.getRecordHistory)

      const { result } = renderHook(() => useRecordHistory('', 0), {
        wrapper: createWrapper(),
      })

      expect(result.current.isLoading).toBe(false)
      expect(mockGetRecordHistory).not.toHaveBeenCalled()
    })
  })

  describe('useLogUserActivity', () => {
    it('should log user activity successfully', async () => {
      const mockLogUserActivity = vi.mocked(auditApiClient.logUserActivity)
      const activityData = {
        action_type: 'LOGIN',
        action_description: 'User logged in',
        category: 'AUTHENTICATION',
      }

      mockLogUserActivity.mockResolvedValue(mockActivityLogs.items[0])

      const { result } = renderHook(() => useLogUserActivity(), {
        wrapper: createWrapper(),
      })

      await result.current.mutateAsync(activityData)

      expect(mockLogUserActivity).toHaveBeenCalledWith(activityData)
      expect(result.current.isSuccess).toBe(true)
    })
  })

  describe('useLogSecurityEvent', () => {
    it('should log security event successfully', async () => {
      const mockLogSecurityEvent = vi.mocked(auditApiClient.logSecurityEvent)
      const securityData = {
        action_type: 'FAILED_LOGIN',
        action_description: 'Failed login attempt',
        severity: 'MEDIUM' as const,
        metadata: { ip: '***********' },
      }

      mockLogSecurityEvent.mockResolvedValue(mockActivityLogs.items[0])

      const { result } = renderHook(() => useLogSecurityEvent(), {
        wrapper: createWrapper(),
      })

      await result.current.mutateAsync(securityData)

      expect(mockLogSecurityEvent).toHaveBeenCalledWith(securityData)
      expect(result.current.isSuccess).toBe(true)
    })
  })

  describe('useLogDataChange', () => {
    it('should log data change successfully', async () => {
      const mockLogDataChange = vi.mocked(auditApiClient.logDataChange)
      const changeData = {
        table_name: 'User',
        record_id: 1,
        operation: 'UPDATE' as const,
        field_name: 'email',
        old_value: '<EMAIL>',
        new_value: '<EMAIL>',
        change_reason: 'User requested change',
      }

      mockLogDataChange.mockResolvedValue(mockAuditTrails.items[0])

      const { result } = renderHook(() => useLogDataChange(), {
        wrapper: createWrapper(),
      })

      await result.current.mutateAsync(changeData)

      expect(mockLogDataChange).toHaveBeenCalledWith(changeData)
      expect(result.current.isSuccess).toBe(true)
    })
  })

  describe('useAuditSummary', () => {
    it('should fetch audit summary successfully', async () => {
      const mockGetAuditSummary = vi.mocked(auditApiClient.getAuditSummary)
      mockGetAuditSummary.mockResolvedValue(mockAuditSummary)

      const { result } = renderHook(() => useAuditSummary(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockAuditSummary)
      expect(mockGetAuditSummary).toHaveBeenCalledWith(undefined)
    })

    it('should fetch audit summary with date range', async () => {
      const mockGetAuditSummary = vi.mocked(auditApiClient.getAuditSummary)
      mockGetAuditSummary.mockResolvedValue(mockAuditSummary)

      const params = {
        start_date: '2023-01-01',
        end_date: '2023-01-31',
      }

      const { result } = renderHook(() => useAuditSummary(params), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockGetAuditSummary).toHaveBeenCalledWith(params)
    })
  })

  describe('useUserActivitySummary', () => {
    it('should fetch user activity summary successfully', async () => {
      const mockGetUserActivitySummary = vi.mocked(auditApiClient.getUserActivitySummary)
      mockGetUserActivitySummary.mockResolvedValue(mockUserActivitySummary)

      const { result } = renderHook(() => useUserActivitySummary(1), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockUserActivitySummary)
      expect(mockGetUserActivitySummary).toHaveBeenCalledWith(1, undefined)
    })

    it('should not fetch when userId is 0', () => {
      const mockGetUserActivitySummary = vi.mocked(auditApiClient.getUserActivitySummary)

      const { result } = renderHook(() => useUserActivitySummary(0), {
        wrapper: createWrapper(),
      })

      expect(result.current.isLoading).toBe(false)
      expect(mockGetUserActivitySummary).not.toHaveBeenCalled()
    })
  })
})
