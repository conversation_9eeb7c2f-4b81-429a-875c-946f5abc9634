"""add_component_table_complete

Revision ID: 4cf07113de3c
Revises: 48e289970994
Create Date: 2025-07-15 18:27:48.891753

"""

from alembic import op
import sqlalchemy as sa
from src.core.models.base import EnumType
from src.core.utils.json_validation import FlexibleJSON


# revision identifiers, used by Alembic.
revision = "4cf07113de3c"
down_revision = "48e289970994"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "Component",
        sa.Column(
            "manufacturer",
            sa.String(length=100),
            nullable=False,
            comment="Component manufacturer name",
        ),
        sa.Column(
            "model_number",
            sa.String(length=100),
            nullable=False,
            comment="Manufacturer's model/part number",
        ),
        sa.Column(
            "description",
            sa.Text(),
            nullable=True,
            comment="Detailed component description",
        ),
        sa.Column(
            "component_type",
            EnumType(),
            nullable=False,
            comment="Type of electrical component",
        ),
        sa.Column(
            "category",
            EnumType(),
            nullable=False,
            comment="Component category for organization",
        ),
        sa.Column(
            "specifications",
            FlexibleJSON(),
            nullable=True,
            comment="Electrical specifications in JSON format",
        ),
        sa.Column(
            "unit_price",
            sa.Numeric(precision=10, scale=2),
            nullable=True,
            comment="Component unit price",
        ),
        sa.Column(
            "currency",
            sa.String(length=3),
            nullable=False,
            comment="Price currency code (ISO 4217)",
        ),
        sa.Column(
            "supplier",
            sa.String(length=100),
            nullable=True,
            comment="Primary supplier name",
        ),
        sa.Column(
            "part_number",
            sa.String(length=100),
            nullable=True,
            comment="Supplier part number",
        ),
        sa.Column(
            "weight_kg",
            sa.Numeric(precision=8, scale=3),
            nullable=True,
            comment="Component weight in kilograms",
        ),
        sa.Column(
            "dimensions_json",
            FlexibleJSON(),
            nullable=True,
            comment="Physical dimensions (L x W x H) in JSON format",
        ),
        sa.Column(
            "is_active",
            sa.Boolean(),
            nullable=False,
            comment="Whether component is active in catalog",
        ),
        sa.Column(
            "is_preferred",
            sa.Boolean(),
            nullable=False,
            comment="Whether component is marked as preferred",
        ),
        sa.Column(
            "stock_status",
            sa.String(length=20),
            nullable=False,
            comment="Current stock availability status",
        ),
        sa.Column(
            "version",
            sa.String(length=20),
            nullable=False,
            comment="Component data version for change tracking",
        ),
        sa.Column(
            "metadata_json",
            FlexibleJSON(),
            nullable=True,
            comment="Additional metadata for component",
        ),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "manufacturer",
            "model_number",
            "is_deleted",
            name="uq_component_manufacturer_model",
        ),
    )
    with op.batch_alter_table("Component", schema=None) as batch_op:
        batch_op.create_index(
            "idx_component_active_preferred",
            ["is_active", "is_preferred"],
            unique=False,
        )
        batch_op.create_index(
            "idx_component_dimensions_gin",
            ["dimensions_json"],
            unique=False,
            postgresql_using="gin",
        )
        batch_op.create_index(
            "idx_component_manufacturer_supplier",
            ["manufacturer", "supplier"],
            unique=False,
        )
        batch_op.create_index(
            "idx_component_metadata_gin",
            ["metadata_json"],
            unique=False,
            postgresql_using="gin",
        )
        batch_op.create_index(
            "idx_component_search",
            ["manufacturer", "model_number", "name"],
            unique=False,
        )
        batch_op.create_index(
            "idx_component_specifications_gin",
            ["specifications"],
            unique=False,
            postgresql_using="gin",
        )
        batch_op.create_index(
            "idx_component_stock_active", ["stock_status", "is_active"], unique=False
        )
        batch_op.create_index(
            "idx_component_type_category", ["component_type", "category"], unique=False
        )
        batch_op.create_index(
            batch_op.f("ix_Component_category"), ["category"], unique=False
        )
        batch_op.create_index(
            batch_op.f("ix_Component_component_type"), ["component_type"], unique=False
        )
        batch_op.create_index(
            batch_op.f("ix_Component_is_active"), ["is_active"], unique=False
        )
        batch_op.create_index(
            batch_op.f("ix_Component_is_preferred"), ["is_preferred"], unique=False
        )
        batch_op.create_index(
            batch_op.f("ix_Component_manufacturer"), ["manufacturer"], unique=False
        )
        batch_op.create_index(
            batch_op.f("ix_Component_model_number"), ["model_number"], unique=False
        )
        batch_op.create_index(
            batch_op.f("ix_Component_part_number"), ["part_number"], unique=False
        )
        batch_op.create_index(
            batch_op.f("ix_Component_stock_status"), ["stock_status"], unique=False
        )
        batch_op.create_index(
            batch_op.f("ix_Component_supplier"), ["supplier"], unique=False
        )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("Component", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_Component_supplier"))
        batch_op.drop_index(batch_op.f("ix_Component_stock_status"))
        batch_op.drop_index(batch_op.f("ix_Component_part_number"))
        batch_op.drop_index(batch_op.f("ix_Component_model_number"))
        batch_op.drop_index(batch_op.f("ix_Component_manufacturer"))
        batch_op.drop_index(batch_op.f("ix_Component_is_preferred"))
        batch_op.drop_index(batch_op.f("ix_Component_is_active"))
        batch_op.drop_index(batch_op.f("ix_Component_component_type"))
        batch_op.drop_index(batch_op.f("ix_Component_category"))
        batch_op.drop_index("idx_component_type_category")
        batch_op.drop_index("idx_component_stock_active")
        batch_op.drop_index("idx_component_specifications_gin", postgresql_using="gin")
        batch_op.drop_index("idx_component_search")
        batch_op.drop_index("idx_component_metadata_gin", postgresql_using="gin")
        batch_op.drop_index("idx_component_manufacturer_supplier")
        batch_op.drop_index("idx_component_dimensions_gin", postgresql_using="gin")
        batch_op.drop_index("idx_component_active_preferred")

    op.drop_table("Component")
    # ### end Alembic commands ###
