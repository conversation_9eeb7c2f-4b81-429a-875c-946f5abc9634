"""Add RBAC and audit trail models

Revision ID: add_rbac_and_audit_trail_models
Revises: 4cf07113de3c
Create Date: 2025-01-18 00:00:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "add_rbac_and_audit_trail_models"
down_revision: Union[str, None] = "4cf07113de3c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create UserRole table
    op.create_table(
        "UserRole",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("is_system_role", sa.<PERSON>olean(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("permissions", sa.Text(), nullable=True),
        sa.Column("parent_role_id", sa.Integer(), nullable=True),
        sa.Column("priority", sa.Integer(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["parent_role_id"],
            ["UserRole.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", name="uq_user_role_name"),
    )

    # Create UserRoleAssignment table
    op.create_table(
        "UserRoleAssignment",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("role_id", sa.Integer(), nullable=False),
        sa.Column("assigned_by_user_id", sa.Integer(), nullable=True),
        sa.Column("assigned_at", sa.DateTime(), nullable=False),
        sa.Column("expires_at", sa.DateTime(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("assignment_context", sa.String(length=255), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["assigned_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["UserRole.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id", "role_id", name="uq_user_role_assignment"),
    )

    # Create ActivityLog table
    op.create_table(
        "ActivityLog",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.Column("session_id", sa.String(length=255), nullable=True),
        sa.Column("action_type", sa.String(length=100), nullable=False),
        sa.Column("action_description", sa.Text(), nullable=False),
        sa.Column("target_type", sa.String(length=100), nullable=True),
        sa.Column("target_id", sa.Integer(), nullable=True),
        sa.Column("target_name", sa.String(length=255), nullable=True),
        sa.Column("request_method", sa.String(length=10), nullable=True),
        sa.Column("request_path", sa.String(length=500), nullable=True),
        sa.Column("request_ip", sa.String(length=45), nullable=True),
        sa.Column("user_agent", sa.String(length=500), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("severity", sa.String(length=50), nullable=False),
        sa.Column("metadata", sa.Text(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("execution_time_ms", sa.Integer(), nullable=True),
        sa.Column("category", sa.String(length=100), nullable=True),
        sa.Column("tags", sa.String(length=500), nullable=True),
        sa.Column("is_security_related", sa.Boolean(), nullable=False),
        sa.Column("is_data_change", sa.Boolean(), nullable=False),
        sa.Column("is_system_event", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )

    # Create AuditTrail table
    op.create_table(
        "AuditTrail",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("activity_log_id", sa.Integer(), nullable=True),
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.Column("changed_at", sa.DateTime(), nullable=False),
        sa.Column("table_name", sa.String(length=100), nullable=False),
        sa.Column("record_id", sa.Integer(), nullable=False),
        sa.Column("operation", sa.String(length=20), nullable=False),
        sa.Column("field_name", sa.String(length=100), nullable=True),
        sa.Column("old_value", sa.Text(), nullable=True),
        sa.Column("new_value", sa.Text(), nullable=True),
        sa.Column("change_reason", sa.String(length=255), nullable=True),
        sa.Column("change_context", sa.Text(), nullable=True),
        sa.Column("is_sensitive", sa.Boolean(), nullable=False),
        sa.Column("is_system_change", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["activity_log_id"],
            ["ActivityLog.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )

    # Add indexes for better performance
    op.create_index(
        op.f("ix_UserRole_is_active"), "UserRole", ["is_active"], unique=False
    )
    op.create_index(
        op.f("ix_UserRole_is_system_role"), "UserRole", ["is_system_role"], unique=False
    )
    op.create_index(
        op.f("ix_UserRoleAssignment_user_id"),
        "UserRoleAssignment",
        ["user_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_UserRoleAssignment_role_id"),
        "UserRoleAssignment",
        ["role_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_UserRoleAssignment_is_active"),
        "UserRoleAssignment",
        ["is_active"],
        unique=False,
    )
    op.create_index(
        op.f("ix_ActivityLog_user_id"), "ActivityLog", ["user_id"], unique=False
    )
    op.create_index(
        op.f("ix_ActivityLog_action_type"), "ActivityLog", ["action_type"], unique=False
    )
    op.create_index(
        op.f("ix_ActivityLog_created_at"), "ActivityLog", ["created_at"], unique=False
    )
    op.create_index(
        op.f("ix_ActivityLog_is_security_related"),
        "ActivityLog",
        ["is_security_related"],
        unique=False,
    )
    op.create_index(
        op.f("ix_ActivityLog_severity"), "ActivityLog", ["severity"], unique=False
    )
    op.create_index(
        op.f("ix_AuditTrail_table_name_record_id"),
        "AuditTrail",
        ["table_name", "record_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_AuditTrail_changed_at"), "AuditTrail", ["changed_at"], unique=False
    )
    op.create_index(
        op.f("ix_AuditTrail_user_id"), "AuditTrail", ["user_id"], unique=False
    )
    op.create_index(
        op.f("ix_AuditTrail_operation"), "AuditTrail", ["operation"], unique=False
    )


def downgrade() -> None:
    # Drop indexes
    op.drop_index(op.f("ix_AuditTrail_operation"), table_name="AuditTrail")
    op.drop_index(op.f("ix_AuditTrail_user_id"), table_name="AuditTrail")
    op.drop_index(op.f("ix_AuditTrail_changed_at"), table_name="AuditTrail")
    op.drop_index(op.f("ix_AuditTrail_table_name_record_id"), table_name="AuditTrail")
    op.drop_index(op.f("ix_ActivityLog_severity"), table_name="ActivityLog")
    op.drop_index(op.f("ix_ActivityLog_is_security_related"), table_name="ActivityLog")
    op.drop_index(op.f("ix_ActivityLog_created_at"), table_name="ActivityLog")
    op.drop_index(op.f("ix_ActivityLog_action_type"), table_name="ActivityLog")
    op.drop_index(op.f("ix_ActivityLog_user_id"), table_name="ActivityLog")
    op.drop_index(
        op.f("ix_UserRoleAssignment_is_active"), table_name="UserRoleAssignment"
    )
    op.drop_index(
        op.f("ix_UserRoleAssignment_role_id"), table_name="UserRoleAssignment"
    )
    op.drop_index(
        op.f("ix_UserRoleAssignment_user_id"), table_name="UserRoleAssignment"
    )
    op.drop_index(op.f("ix_UserRole_is_system_role"), table_name="UserRole")
    op.drop_index(op.f("ix_UserRole_is_active"), table_name="UserRole")

    # Drop tables
    op.drop_table("AuditTrail")
    op.drop_table("ActivityLog")
    op.drop_table("UserRoleAssignment")
    op.drop_table("UserRole")
