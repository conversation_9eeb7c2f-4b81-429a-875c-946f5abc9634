"""Pydantic schemas for User Role and Role Assignment models.

This module defines request/response schemas for the RBAC system,
including user roles, role assignments, and related operations.
"""

import datetime
from typing import Any, List, Optional

from pydantic import BaseModel, Field, validator

from src.core.schemas.base import BaseSchema


class UserRoleBase(BaseModel):
    """Base schema for UserRole with common fields."""

    name: str = Field(..., min_length=1, max_length=100, description="Role name")
    description: Optional[str] = Field(
        None, max_length=1000, description="Role description"
    )
    is_system_role: bool = Field(
        False, description="Whether this is a system-defined role"
    )
    is_active: bool = Field(True, description="Whether the role is active")
    permissions: Optional[str] = Field(None, description="JSON string of permissions")
    parent_role_id: Optional[int] = Field(
        None, description="ID of parent role for hierarchy"
    )
    priority: int = Field(0, ge=0, le=100, description="Priority for role resolution")
    notes: Optional[str] = Field(None, max_length=500, description="Additional notes")

    @validator("name")
    def validate_name(cls, v: str) -> str:
        """Validate and clean the role name."""
        if not v or not v.strip():
            raise ValueError("Role name cannot be empty")
        return v.strip()

    @validator("permissions")
    def validate_permissions(cls, v: Optional[str]) -> Optional[str]:
        """Validate that permissions field contains valid JSON."""
        if v is not None:
            try:
                import json

                json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("Permissions must be valid JSON")
        return v


class UserRoleCreate(UserRoleBase):
    """Schema for creating a new user role."""

    pass


class UserRoleUpdate(BaseModel):
    """Schema for updating an existing user role."""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=1000)
    is_active: Optional[bool] = None
    permissions: Optional[str] = None
    parent_role_id: Optional[int] = None
    priority: Optional[int] = Field(None, ge=0, le=100)
    notes: Optional[str] = Field(None, max_length=500)

    @validator("name")
    def validate_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate and clean the role name for updates."""
        if v is not None and (not v or not v.strip()):
            raise ValueError("Role name cannot be empty")
        return v.strip() if v else v

    @validator("permissions")
    def validate_permissions(cls, v: Optional[str]) -> Optional[str]:
        """Validate that permissions field contains valid JSON for updates."""
        if v is not None:
            try:
                import json

                json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("Permissions must be valid JSON")
        return v


class UserRoleResponse(BaseSchema):
    """Schema for user role responses."""

    id: int
    name: str
    description: Optional[str]
    is_system_role: bool
    is_active: bool
    permissions: Optional[str]
    parent_role_id: Optional[int]
    priority: int
    notes: Optional[str]
    created_at: datetime.datetime
    updated_at: datetime.datetime
    is_deleted: bool
    deleted_at: Optional[datetime.datetime]
    deleted_by_user_id: Optional[int]

    class Config:
        """Pydantic configuration for UserRoleResponse."""

        from_attributes = True


class UserRoleAssignmentBase(BaseModel):
    """Base schema for UserRoleAssignment with common fields."""

    user_id: int = Field(..., description="ID of the user")
    role_id: int = Field(..., description="ID of the role")
    assigned_by_user_id: Optional[int] = Field(
        None, description="ID of user who made the assignment"
    )
    expires_at: Optional[datetime.datetime] = Field(
        None, description="When the assignment expires"
    )
    is_active: bool = Field(True, description="Whether the assignment is active")
    assignment_context: Optional[str] = Field(
        None, max_length=255, description="Context for the assignment"
    )
    notes: Optional[str] = Field(None, max_length=500, description="Additional notes")

    @validator("expires_at")
    def validate_expires_at(
        cls, v: Optional[datetime.datetime]
    ) -> Optional[datetime.datetime]:
        """Validate that expiration date is in the future."""
        if v is not None and v <= datetime.datetime.now(datetime.timezone.utc):
            raise ValueError("Expiration date must be in the future")
        return v


class UserRoleAssignmentCreate(UserRoleAssignmentBase):
    """Schema for creating a new user role assignment."""

    name: str = Field(..., min_length=1, max_length=100, description="Assignment name")

    @validator("name")
    def validate_name(cls, v: str) -> str:
        """Validate and clean the assignment name."""
        if not v or not v.strip():
            raise ValueError("Assignment name cannot be empty")
        return v.strip()


class UserRoleAssignmentUpdate(BaseModel):
    """Schema for updating an existing user role assignment."""

    expires_at: Optional[datetime.datetime] = None
    is_active: Optional[bool] = None
    assignment_context: Optional[str] = Field(None, max_length=255)
    notes: Optional[str] = Field(None, max_length=500)

    @validator("expires_at")
    def validate_expires_at(
        cls, v: Optional[datetime.datetime]
    ) -> Optional[datetime.datetime]:
        """Validate that expiration date is in the future for updates."""
        if v is not None and v <= datetime.datetime.now(datetime.timezone.utc):
            raise ValueError("Expiration date must be in the future")
        return v


class UserRoleAssignmentResponse(BaseSchema):
    """Schema for user role assignment responses."""

    id: int
    name: str
    user_id: int
    role_id: int
    assigned_by_user_id: Optional[int]
    assigned_at: datetime.datetime
    expires_at: Optional[datetime.datetime]
    is_active: bool
    assignment_context: Optional[str]
    notes: Optional[str]
    created_at: datetime.datetime
    updated_at: datetime.datetime
    is_deleted: bool
    deleted_at: Optional[datetime.datetime]
    deleted_by_user_id: Optional[int]
    is_expired: bool

    class Config:
        """Pydantic configuration for UserRoleAssignmentResponse."""

        from_attributes = True


class UserRoleHierarchy(BaseModel):
    """Schema for representing role hierarchy."""

    id: int
    name: str
    description: Optional[str]
    priority: int
    parent_role_id: Optional[int]
    child_roles: List["UserRoleHierarchy"] = []

    class Config:
        """Pydantic configuration for UserRoleHierarchy."""

        from_attributes = True


class UserRolePermissions(BaseModel):
    """Schema for role permissions."""

    role_id: int
    role_name: str
    permissions: List[str]
    inherited_permissions: List[str] = []
    effective_permissions: List[str] = []

    class Config:
        """Pydantic configuration for UserRolePermissions."""

        from_attributes = True


class UserRolesSummary(BaseModel):
    """Schema for user roles summary."""

    user_id: int
    active_roles: List[UserRoleResponse]
    expired_roles: List[UserRoleResponse]
    inactive_roles: List[UserRoleResponse]
    effective_permissions: List[str]

    class Config:
        """Pydantic configuration for UserRolesSummary."""

        from_attributes = True


# Update forward references
UserRoleHierarchy.model_rebuild()
