#!/usr/bin/env python3
"""Component Category Service for Ultimate Electrical Designer.

This module provides comprehensive business logic for component category management
operations, including CRUD operations, validation, hierarchical operations, and
category organization for electrical components.

Key Features:
- Complete category lifecycle management (create, read, update, delete)
- Hierarchical category operations and tree management
- Category validation and business rule enforcement
- Bulk operations support with transaction management
- Dependency resolution and conflict detection
- Audit logging and change tracking
- Professional electrical design standards compliance
"""

from typing import Any, Dict, List, Optional, Tuple

from src.config.logging_config import logger
from src.core.errors.exceptions import (
    BusinessLogicError,
    NotFoundError,
    ValidationError,
)
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.models.general.component_category import ComponentCategory
from src.core.monitoring.unified_performance_monitor import monitor_service_performance
from src.core.repositories.general.component_category_repository import (
    ComponentCategoryRepository,
)
from src.core.schemas.general.component_category_schemas import (
    ComponentCategoryCreateSchema,
    ComponentCategoryListResponseSchema,
    ComponentCategoryReadSchema,
    ComponentCategorySearchSchema,
    ComponentCategorySummarySchema,
    ComponentCategoryTreeNodeSchema,
    ComponentCategoryTreeResponseSchema,
    ComponentCategoryUpdateSchema,
    ComponentCategoryValidationResultSchema,
)
from src.core.utils.pagination_utils import PaginationParams


class ComponentCategoryService:
    """Service for component category business logic operations.

    This service provides comprehensive business logic for component category
    management, including validation, hierarchical operations, and
    professional electrical design standards compliance.
    """

    def __init__(self, category_repo: ComponentCategoryRepository):
        """Initialize service with repository dependency.

        Args:
            category_repo: ComponentCategoryRepository instance

        """
        self.category_repo = category_repo

    @handle_service_errors("create_component_category")
    @monitor_service_performance("create_component_category")
    def create_category(
        self, category_data: ComponentCategoryCreateSchema
    ) -> ComponentCategoryReadSchema:
        """Create a new component category.

        Args:
            category_data: Category creation data

        Returns:
            ComponentCategoryReadSchema: Created category data

        Raises:
            ValidationError: If category data is invalid
            BusinessLogicError: If business rules are violated

        """
        logger.info(f"Creating component category: {category_data.name}")

        # Validate category data
        validation_result = self._validate_category_data(category_data)
        if not validation_result.is_valid:
            raise ValidationError(
                f"Category validation failed: {', '.join(validation_result.errors)}"
            )

        # Check for duplicate categories
        existing = self.category_repo.get_by_name(
            category_data.name, category_data.parent_category_id
        )
        if existing:
            raise BusinessLogicError(
                detail=f"Category '{category_data.name}' already exists in this scope"
            )

        # Validate hierarchy if parent is specified
        if category_data.parent_category_id:
            parent = self.category_repo.get_by_id(category_data.parent_category_id)
            if not parent or parent.is_deleted:
                raise ValidationError("Parent category not found")

        # Create category
        category_dict = category_data.model_dump(exclude_unset=True)
        category = self.category_repo.create(category_dict)

        logger.info(f"Created component category: {category.id}")
        return self._convert_to_read_schema(category)

    @handle_service_errors("get_component_category")
    @monitor_service_performance("get_component_category")
    def get_category(self, category_id: int) -> ComponentCategoryReadSchema:
        """Get component category by ID.

        Args:
            category_id: Category ID

        Returns:
            ComponentCategoryReadSchema: Category data

        Raises:
            NotFoundError: If category not found

        """
        logger.debug(f"Retrieving component category: {category_id}")

        category = self.category_repo.get_by_id(category_id)
        if not category or category.is_deleted:
            raise NotFoundError(
                code="COMPONENT_CATEGORY_NOT_FOUND",
                detail=f"Component category {category_id} not found",
            )

        return self._convert_to_read_schema(category)

    @handle_service_errors("update_component_category")
    @monitor_service_performance("update_component_category")
    def update_category(
        self, category_id: int, category_data: ComponentCategoryUpdateSchema
    ) -> ComponentCategoryReadSchema:
        """Update component category.

        Args:
            category_id: Category ID
            category_data: Category update data

        Returns:
            ComponentCategoryReadSchema: Updated category data

        Raises:
            NotFoundError: If category not found
            ValidationError: If update data is invalid
            BusinessLogicError: If business rules are violated

        """
        logger.info(f"Updating component category: {category_id}")

        # Validate category exists
        category = self.category_repo.get_by_id(category_id)
        if not category or category.is_deleted:
            raise NotFoundError(
                code="COMPONENT_CATEGORY_NOT_FOUND",
                detail=f"Component category {category_id} not found",
            )

        # Validate hierarchy changes
        if category_data.parent_category_id is not None:
            if not self.category_repo.validate_hierarchy(
                category_id, category_data.parent_category_id
            ):
                raise BusinessLogicError(
                    detail="Invalid hierarchy: would create circular reference"
                )
        elif (
            hasattr(category_data, "parent_category_id")
            and category_data.parent_category_id is None
        ):
            # Explicitly moving to root level - validate this is allowed
            logger.debug(f"Moving category {category_id} to root level")

        # Check for name conflicts if name is being changed
        if category_data.name and category_data.name != category.name:
            existing = self.category_repo.get_by_name(
                category_data.name,
                category_data.parent_category_id or category.parent_category_id,
            )
            if existing and existing.id != category_id:
                raise BusinessLogicError(
                    detail=f"Category '{category_data.name}' already exists in this scope"
                )

        # Update category
        update_dict = category_data.model_dump(exclude_unset=True)
        updated_category = self.category_repo.update(category_id, update_dict)

        logger.info(f"Updated component category: {category_id}")
        return self._convert_to_read_schema(updated_category)

    @handle_service_errors("delete_component_category")
    @monitor_service_performance("delete_component_category")
    def delete_category(
        self, category_id: int, deleted_by_user_id: Optional[int] = None
    ) -> bool:
        """Soft delete a component category with dependency checking.

        Args:
            category_id: Category ID
            deleted_by_user_id: ID of user performing deletion

        Returns:
            bool: True if deleted successfully

        Raises:
            NotFoundError: If category not found
            BusinessLogicError: If category has dependencies

        """
        logger.info(f"Deleting component category: {category_id}")

        # Validate category exists
        category = self.category_repo.get_by_id(category_id)
        if not category or category.is_deleted:
            raise NotFoundError(
                code="COMPONENT_CATEGORY_NOT_FOUND",
                detail=f"Component category {category_id} not found",
            )

        # Check if category can be deleted
        can_delete, reason = category.can_delete()
        if not can_delete:
            raise BusinessLogicError(detail=f"Cannot delete category: {reason}")

        # Perform soft delete
        category.soft_delete(deleted_by_user_id)
        self.category_repo.db_session.commit()

        logger.info(f"Deleted component category: {category_id}")
        return True

    @handle_service_errors("copy_component_category")
    @monitor_service_performance("copy_component_category")
    def copy_category(
        self,
        category_id: int,
        target_parent_id: Optional[int] = None,
        copy_children: bool = False,
        name_suffix: str = " (Copy)",
    ) -> ComponentCategoryReadSchema:
        """Copy a component category and optionally its children.

        Args:
            category_id: ID of category to copy
            target_parent_id: Target parent category ID
            copy_children: Whether to copy child categories
            name_suffix: Suffix to add to copied category name

        Returns:
            ComponentCategoryReadSchema: Copied category data

        Raises:
            NotFoundError: If category not found
            BusinessLogicError: If copy operation would violate business rules

        """
        logger.info(f"Copying component category: {category_id}")

        # Get source category
        source_category = self.category_repo.get_by_id(category_id)
        if not source_category or source_category.is_deleted:
            raise NotFoundError(
                code="COMPONENT_CATEGORY_NOT_FOUND",
                detail=f"Component category {category_id} not found",
            )

        # Validate target parent if specified
        if target_parent_id:
            target_parent = self.category_repo.get_by_id(target_parent_id)
            if not target_parent or target_parent.is_deleted:
                raise ValidationError(
                    message=f"Target parent category {target_parent_id} not found",
                    error_code="TARGET_PARENT_NOT_FOUND",
                )

        # Create copy data
        copy_name = source_category.name + name_suffix

        # Ensure unique name in target scope
        counter = 1
        original_copy_name = copy_name
        while self.category_repo.get_by_name(copy_name, target_parent_id):
            copy_name = f"{original_copy_name} ({counter})"
            counter += 1

        copy_data = ComponentCategoryCreateSchema(
            name=copy_name,
            description=source_category.description,
            parent_category_id=target_parent_id,
            is_active=source_category.is_active,
        )

        # Create the copy
        copied_category = self.create_category(copy_data)

        # Copy children if requested
        if copy_children:
            children = self.category_repo.get_children(category_id)
            for child in children:
                self.copy_category(
                    child.id,
                    copied_category.id,
                    copy_children=True,
                    name_suffix="",  # Don't add suffix to children
                )

        logger.info(f"Copied component category: {copied_category.id}")
        return copied_category

    @handle_service_errors("list_component_categories")
    @monitor_service_performance("list_component_categories")
    def list_categories(
        self,
        search_schema: Optional[ComponentCategorySearchSchema] = None,
        pagination: Optional[PaginationParams] = None,
    ) -> ComponentCategoryListResponseSchema:
        """List component categories with filtering and pagination.

        Args:
            search_schema: Optional search parameters
            pagination: Optional pagination parameters

        Returns:
            ComponentCategoryListResponseSchema: Paginated category list

        """
        logger.debug("Listing component categories")

        # Set defaults
        if search_schema is None:
            search_schema = ComponentCategorySearchSchema()
        if pagination is None:
            pagination = PaginationParams()

        # Get categories
        categories, total_count = self.category_repo.search_categories(
            search_schema, pagination
        )

        # Convert to summary schemas
        category_summaries = [
            self._convert_to_summary_schema(category) for category in categories
        ]

        # Build response
        response = ComponentCategoryListResponseSchema(
            categories=category_summaries,
            total_count=total_count,
            page=pagination.page,
            page_size=pagination.limit,
            total_pages=(total_count + pagination.limit - 1) // pagination.limit,
            has_next=pagination.offset + len(categories) < total_count,
            has_previous=pagination.page > 1,
        )

        logger.debug(f"Listed {len(categories)} categories (total: {total_count})")
        return response

    @handle_service_errors("get_category_tree")
    @monitor_service_performance("get_category_tree")
    def get_category_tree(
        self, root_id: Optional[int] = None
    ) -> ComponentCategoryTreeResponseSchema:
        """Get hierarchical category tree.

        Args:
            root_id: Optional root category ID

        Returns:
            ComponentCategoryTreeResponseSchema: Hierarchical category tree

        """
        logger.info(f"SERVICE: Getting category tree from root: {root_id}")

        try:
            # Get tree data
            tree_categories = self.category_repo.get_category_tree(root_id)
            logger.info(
                f"SERVICE: Retrieved {len(tree_categories)} categories from repository"
            )

            # Convert to tree response
            # This is a simplified implementation - full tree building would be more complex
            tree_nodes = []
            total_categories = len(tree_categories)
            max_depth = 0

            for category in tree_categories:
                # Calculate level based on hierarchy (0 for root categories)
                level = 0 if category.parent_category_id is None else 1

                # Count component types in this category
                component_count = (
                    len(category.component_types) if category.component_types else 0
                )

                tree_node = ComponentCategoryTreeNodeSchema(
                    id=category.id,
                    name=category.name,
                    description=category.description,
                    is_active=category.is_active,
                    level=level,
                    component_count=component_count,
                    children=[],  # Would need recursive building for full tree
                )
                tree_nodes.append(tree_node)
                max_depth = max(max_depth, level)

            response = ComponentCategoryTreeResponseSchema(
                tree=tree_nodes,
                total_categories=total_categories,
                max_depth=max_depth,
            )

            logger.info(
                f"SERVICE: Successfully created tree response with {len(tree_nodes)} nodes"
            )
            return response
        except Exception as e:
            logger.error(f"SERVICE: Error in get_category_tree: {str(e)}")
            raise

    def _validate_category_data(
        self, category_data: ComponentCategoryCreateSchema
    ) -> ComponentCategoryValidationResultSchema:
        """Validate category creation data.

        Args:
            category_data: Category data to validate

        Returns:
            ComponentCategoryValidationResultSchema: Validation result

        """
        errors = []
        warnings = []

        # Basic validation (Pydantic handles most of this)
        if not category_data.name or len(category_data.name.strip()) == 0:
            errors.append("Category name is required")

        # Business rule validation
        if category_data.parent_category_id:
            parent = self.category_repo.get_by_id(category_data.parent_category_id)
            if not parent or parent.is_deleted:
                errors.append("Parent category does not exist")
            elif not parent.is_active:
                warnings.append("Parent category is inactive")

        return ComponentCategoryValidationResultSchema(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            category_id=None,
        )

    def _convert_to_read_schema(
        self, category: ComponentCategory
    ) -> ComponentCategoryReadSchema:
        """Convert ComponentCategory model to read schema.

        Args:
            category: ComponentCategory model instance

        Returns:
            ComponentCategoryReadSchema: Read schema

        """
        return ComponentCategoryReadSchema(
            id=category.id,
            name=category.name,
            description=category.description,
            parent_category_id=category.parent_category_id,
            is_active=category.is_active,
            full_path=category.full_path,
            level=category.level,
            is_root_category=category.is_root_category,
            has_children=category.has_children,
            component_count=category.component_count,
            created_at=category.created_at,
            updated_at=category.updated_at,
        )

    def _convert_to_summary_schema(
        self, category: ComponentCategory
    ) -> ComponentCategorySummarySchema:
        """Convert ComponentCategory model to summary schema.

        Args:
            category: ComponentCategory model instance

        Returns:
            ComponentCategorySummarySchema: Summary schema

        """
        return ComponentCategorySummarySchema(
            id=category.id,
            name=category.name,
            description=category.description,
            parent_category_id=category.parent_category_id,
            is_active=category.is_active,
            component_count=category.component_count,
            child_count=len(category.child_categories),
        )
