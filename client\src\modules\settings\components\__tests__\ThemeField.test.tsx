/**
 * @jest-environment jsdom
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, expect, it, beforeEach, afterEach } from 'vitest'
import { ThemeField, useIsThemeField } from '../ThemeField'
import type { SettingsField } from '../../types'

// Mock the theme provider
const mockSetTheme = vi.fn()
const mockUseUEDTheme = {
  theme: 'light',
  setTheme: mockSetTheme,
  themes: ['light', 'dark', 'system'],
  systemTheme: 'light',
  resolvedTheme: 'light',
  themeConfig: {
    name: 'light',
    label: 'Light',
    colors: {
      primary: '#000000',
      secondary: '#ffffff',
      background: '#ffffff',
      foreground: '#000000',
      muted: '#f5f5f5',
      accent: '#e5e5e5',
      destructive: '#ff0000',
      border: '#e5e5e5',
      input: '#ffffff',
      ring: '#000000',
    },
  },
  mounted: true,
}

vi.mock('../../../../components/theme-provider', () => ({
  useUEDTheme: () => mockUseUEDTheme,
}))

// Mock UI components
vi.mock('../../../../components/ui/label', () => ({
  Label: ({ children, ...props }: any) => <label {...props}>{children}</label>,
}))

vi.mock('../../../../components/ui/theme-toggle', () => ({
  ThemeToggleWithLabel: ({ label, ...props }: any) => (
    <button data-testid="theme-toggle" {...props}>
      {label}
    </button>
  ),
}))

vi.mock('../../../../components/ui/alert', () => ({
  Alert: ({ children }: any) => <div data-testid="alert">{children}</div>,
  AlertDescription: ({ children }: any) => <div data-testid="alert-description">{children}</div>,
}))

const mockField: SettingsField = {
  id: 'theme',
  label: 'Color Theme',
  type: 'select',
  value: 'light',
  options: [
    { value: 'light', label: 'Light' },
    { value: 'dark', label: 'Dark' },
    { value: 'system', label: 'System Default' },
  ],
}

describe('ThemeField', () => {
  const mockOnFieldChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly when mounted', () => {
    render(
      <ThemeField
        field={mockField}
        currentValue="light"
        hasError={false}
        onFieldChange={mockOnFieldChange}
      />
    )

    expect(screen.getByText('Color Theme')).toBeInTheDocument()
    expect(screen.getByTestId('theme-toggle')).toBeInTheDocument()
    expect(screen.getByText('Current:')).toBeInTheDocument()
    expect(screen.getByText('light')).toBeInTheDocument()
  })

  it('shows loading state when not mounted', () => {
    const unmountedTheme = { ...mockUseUEDTheme, mounted: false }
    vi.mocked(require('../../../../components/theme-provider').useUEDTheme).mockReturnValue(
      unmountedTheme
    )

    render(
      <ThemeField
        field={mockField}
        currentValue="light"
        hasError={false}
        onFieldChange={mockOnFieldChange}
      />
    )

    expect(screen.getByText('Loading...')).toBeInTheDocument()
    expect(screen.getByText('Color Theme')).toBeInTheDocument()
  })

  it('displays current theme correctly for system theme', () => {
    const systemTheme = { ...mockUseUEDTheme, theme: 'system', resolvedTheme: 'dark' }
    vi.mocked(require('../../../../components/theme-provider').useUEDTheme).mockReturnValue(
      systemTheme
    )

    render(
      <ThemeField
        field={mockField}
        currentValue="system"
        hasError={false}
        onFieldChange={mockOnFieldChange}
      />
    )

    expect(screen.getByText('System (dark)')).toBeInTheDocument()
  })

  it('shows required indicator when field is required', () => {
    const requiredField = {
      ...mockField,
      validation: { required: true },
    }

    render(
      <ThemeField
        field={requiredField}
        currentValue="light"
        hasError={false}
        onFieldChange={mockOnFieldChange}
      />
    )

    expect(screen.getByText('*')).toBeInTheDocument()
  })

  it('displays error state correctly', () => {
    render(
      <ThemeField
        field={mockField}
        currentValue="light"
        hasError={true}
        errorMessage="Theme selection is required"
        onFieldChange={mockOnFieldChange}
      />
    )

    expect(screen.getByText('Theme selection is required')).toBeInTheDocument()
    expect(screen.getByRole('alert')).toBeInTheDocument()
  })

  it('shows field description when provided', () => {
    const fieldWithDescription = {
      ...mockField,
      description: 'Choose your preferred color scheme',
    }

    render(
      <ThemeField
        field={fieldWithDescription}
        currentValue="light"
        hasError={false}
        onFieldChange={mockOnFieldChange}
      />
    )

    expect(screen.getByText('Choose your preferred color scheme')).toBeInTheDocument()
  })

  it('calls onFieldChange when theme changes', async () => {
    render(
      <ThemeField
        field={mockField}
        currentValue="light"
        hasError={false}
        onFieldChange={mockOnFieldChange}
      />
    )

    // Simulate theme change
    const newTheme = 'dark'
    const updatedTheme = { ...mockUseUEDTheme, theme: newTheme }
    vi.mocked(require('../../../../components/theme-provider').useUEDTheme).mockReturnValue(
      updatedTheme
    )

    // Re-render to trigger useEffect
    render(
      <ThemeField
        field={mockField}
        currentValue="light"
        hasError={false}
        onFieldChange={mockOnFieldChange}
      />
    )

    await waitFor(() => {
      expect(mockOnFieldChange).toHaveBeenCalledWith('theme', newTheme)
    })
  })

  it('shows appropriate theme description for light theme', () => {
    render(
      <ThemeField
        field={mockField}
        currentValue="light"
        hasError={false}
        onFieldChange={mockOnFieldChange}
      />
    )

    expect(screen.getByText(/Light theme with bright colors/)).toBeInTheDocument()
  })

  it('shows appropriate theme description for dark theme', () => {
    const darkTheme = { ...mockUseUEDTheme, theme: 'dark' }
    vi.mocked(require('../../../../components/theme-provider').useUEDTheme).mockReturnValue(
      darkTheme
    )

    render(
      <ThemeField
        field={mockField}
        currentValue="dark"
        hasError={false}
        onFieldChange={mockOnFieldChange}
      />
    )

    expect(screen.getByText(/Dark theme with muted colors/)).toBeInTheDocument()
  })

  it('shows appropriate theme description for system theme', () => {
    const systemTheme = { ...mockUseUEDTheme, theme: 'system' }
    vi.mocked(require('../../../../components/theme-provider').useUEDTheme).mockReturnValue(
      systemTheme
    )

    render(
      <ThemeField
        field={mockField}
        currentValue="system"
        hasError={false}
        onFieldChange={mockOnFieldChange}
      />
    )

    expect(screen.getByText(/Automatically switches between light and dark/)).toBeInTheDocument()
  })

  it('applies custom className', () => {
    const { container } = render(
      <ThemeField
        field={mockField}
        currentValue="light"
        hasError={false}
        onFieldChange={mockOnFieldChange}
        className="custom-class"
      />
    )

    expect(container.firstChild).toHaveClass('custom-class')
  })
})

describe('useIsThemeField', () => {
  it('returns true for theme select field', () => {
    const themeField: SettingsField = {
      id: 'theme',
      label: 'Theme',
      type: 'select',
      value: 'light',
    }

    const result = useIsThemeField(themeField)
    expect(result).toBe(true)
  })

  it('returns false for non-theme field', () => {
    const nonThemeField: SettingsField = {
      id: 'language',
      label: 'Language',
      type: 'select',
      value: 'en',
    }

    const result = useIsThemeField(nonThemeField)
    expect(result).toBe(false)
  })

  it('returns false for theme field with different type', () => {
    const themeTextField: SettingsField = {
      id: 'theme',
      label: 'Theme',
      type: 'text',
      value: 'light',
    }

    const result = useIsThemeField(themeTextField)
    expect(result).toBe(false)
  })
})

describe('Theme Field Integration', () => {
  it('integrates properly with settings form', () => {
    render(
      <ThemeField
        field={mockField}
        currentValue="light"
        hasError={false}
        onFieldChange={mockOnFieldChange}
      />
    )

    // Should render all expected elements for settings integration
    expect(screen.getByText('Color Theme')).toBeInTheDocument()
    expect(screen.getByTestId('theme-toggle')).toBeInTheDocument()
    expect(screen.getByTestId('alert')).toBeInTheDocument()
  })
})
