# src/app.py
"""Application Module for Ultimate Electrical Designer.

This module contains the FastAPI application instance and configuration for the Ultimate Electrical Designer server.
"""

from contextlib import asynccontextmanager
from typing import Any, AsyncGenerator, Dict, Optional

from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Import main API router
from src.api.main_router import api_router
from src.config.logging_config import logger
from src.config.settings import settings
from src.core.database import close_engine, initialize_database
from src.core.enums import ErrorContext
from src.core.errors.unified_error_handler import unified_error_handler

# Import unified systems
from src.core.security.unified_security_validator import (
    UnifiedSecurityValidator,
    get_unified_security_validator,
)
from src.middleware.caching_middleware import CachingMiddleware
from src.middleware.context_middleware import ContextMiddleware

# Import middleware
from src.middleware.logging_middleware import LoggingMiddleware
from src.middleware.rate_limiting_middleware import RateLimitingMiddleware

# Import core modules
from src.middleware.security_middleware import SecurityMiddleware


# --- Application Lifespan Events ---
@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Handles startup and shutdown events for the application with robust error handling."""
    logger.info("Application startup initiated.")

    try:
        # Initialize database with migrations and fallback handling
        logger.info("Initializing database...")
        engine = initialize_database(run_migrations=True, create_tables_if_needed=True)
        logger.info(f"Database initialized successfully using: {engine.url}")

        # Store engine in app state for access in other parts of the application
        app.state.db_engine = engine

        # Initialize performance optimizer cache
        from src.core.utils.performance_optimizer import performance_optimizer

        app.state.performance_optimizer = performance_optimizer
        logger.info("Performance optimizer initialized with caching support.")

        # Initialize unified security validator
        unified_security_validator = get_unified_security_validator()
        app.state.unified_security_validator = unified_security_validator
        logger.info("Unified security validator initialized successfully.")

        logger.info("Application startup completed successfully.")
        yield  # Application runs

    except Exception as e:
        logger.critical(f"Application startup failed: {e}", exc_info=True)
        # In production, we might want to exit gracefully
        # For now, re-raise to prevent the app from starting with a broken state
        raise

    finally:
        logger.info("Application shutdown initiated.")

        try:
            # Clean up database connections
            close_engine()
            logger.info("Database connections closed.")

            # Clean up performance optimizer cache
            if (
                hasattr(app.state, "performance_optimizer")
                and app.state.performance_optimizer
            ):
                app.state.performance_optimizer.clear_cache()
                logger.info("Performance optimizer cache cleared.")

        except Exception as e:
            logger.error(f"Error during shutdown cleanup: {e}", exc_info=True)

        logger.info("Application shutdown completed.")


def create_app(db_engine: Optional[Any] = None) -> FastAPI:
    """Creates and configures the FastAPI application."""

    @asynccontextmanager
    async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
        """Handles startup and shutdown events for the application."""
        logger.info("Application startup initiated.")
        try:
            if not hasattr(app.state, "db_engine"):
                if db_engine:
                    engine = db_engine
                else:
                    engine = initialize_database(
                        run_migrations=True, create_tables_if_needed=True
                    )
                app.state.db_engine = engine
                logger.info(f"Database initialized successfully using: {engine.url}")
            yield
        finally:
            logger.info("Application shutdown initiated.")
            if hasattr(app.state, "db_engine"):
                close_engine()
                logger.info("Database connections closed.")

    app = FastAPI(
        title=settings.APP_NAME,
        description=settings.APP_DESCRIPTION,
        version=settings.APP_VERSION,
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        lifespan=lifespan,
    )

    # Register middleware
    # Add other middleware first
    app.add_middleware(ContextMiddleware)
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(CachingMiddleware)
    app.add_middleware(
        RateLimitingMiddleware,
        default_requests_per_minute=settings.RATE_LIMIT_DEFAULT_REQUESTS_PER_MINUTE,
        default_burst_size=20,  # Allow more burst requests for testing
    )
    app.add_middleware(SecurityMiddleware)

    # CORS middleware should be added last to execute first and handle preflight requests
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[
            "http://localhost:3000",  # Next.js default port
            "http://localhost:3001",  # Alternative Next.js port
            "http://localhost:3002",  # Alternative Next.js port
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001",
            "http://127.0.0.1:3002",
        ]
        if settings.DEBUG
        else [],  # Only allow in development
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        allow_headers=["*"],
    )

    # Register exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(
        request: Request, exc: Exception
    ) -> JSONResponse:
        logger.error(
            f"Global exception handler caught exception for {request.method} {request.url.path}: {type(exc).__name__}: {str(exc)}"
        )
        logger.error(f"Exception details: {exc}", exc_info=True)
        additional_context = {
            "path": request.url.path,
            "method": request.method,
        }
        result = unified_error_handler.handle_exception(
            exc, ErrorContext.API, request, additional_context
        )
        logger.error(
            f"Global exception handler returning status code: {result.http_status_code}"
        )
        return result.to_json_response()

    # Include API routers
    app.include_router(api_router, prefix="/api")

    @app.get("/")
    async def read_root() -> Dict[str, str]:
        return {
            "message": f"Welcome to {settings.APP_NAME} API v{settings.APP_VERSION}"
        }

    return app


app = create_app()
