"""Performance Monitor Module
Re-exports from unified_performance_monitor for backward compatibility.
"""

from src.core.monitoring.unified_performance_monitor import (
    PerformanceMetrics,
    UnifiedPerformanceMonitor,
    monitor_api_performance,
    monitor_service_performance,
    monitor_repository_performance,
    monitor_database_performance,
    monitor_async_operation,
    get_performance_summary,
    get_operation_metrics,
    create_performance_report,
    get_unified_performance_monitor,
)

# Backward compatibility aliases
PerformanceMonitor = UnifiedPerformanceMonitor
monitor_performance = monitor_api_performance
measure_execution_time = monitor_service_performance
track_memory_usage = monitor_async_operation
monitor_database_queries = monitor_database_performance
log_performance_metrics = get_performance_summary

__all__ = [
    "PerformanceMetrics",
    "PerformanceMonitor",
    "UnifiedPerformanceMonitor",
    "monitor_performance",
    "monitor_api_performance",
    "monitor_service_performance",
    "monitor_repository_performance",
    "monitor_database_performance",
    "monitor_async_operation",
    "measure_execution_time",
    "track_memory_usage",
    "monitor_database_queries",
    "log_performance_metrics",
    "get_performance_summary",
    "get_operation_metrics",
    "create_performance_report",
    "get_unified_performance_monitor",
]
