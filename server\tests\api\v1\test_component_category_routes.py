#!/usr/bin/env python3
"""Tests for Component Category functionality.

This module provides comprehensive tests for component category management,
including models, repositories, services, and API endpoints.

Key Test Areas:
- ComponentCategory model validation and business logic
- ComponentCategoryRepository data access operations
- ComponentCategoryService business logic and validation
- Component Category API endpoints and error handling
- Hierarchical operations and tree management
- Performance and edge case testing
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.models.general.component_category import ComponentCategory
from src.core.repositories.general.component_category_repository import (
    ComponentCategoryRepository,
)
from src.core.services.general.component_category_service import (
    ComponentCategoryService,
)
from src.core.schemas.general.component_category_schemas import (
    ComponentCategoryCreateSchema,
    ComponentCategoryUpdateSchema,
    ComponentCategorySearchSchema,
)
from src.core.errors.exceptions import (
    BusinessLogicError,
    NotFoundError,
    ValidationError,
)
from src.core.utils.pagination_utils import PaginationParams


class TestComponentCategoryAPI:
    """Test Component Category API endpoints."""

    def test_create_category_endpoint(self, authenticated_client: TestClient):
        """Test POST /component-categories/ endpoint."""
        category_data = {
            "name": "API Test Category",
            "description": "API test description",
            "is_active": True,
        }

        response = authenticated_client.post(
            "/api/v1/component-categories/",
            json=category_data,
        )

        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "API Test Category"
        assert data["description"] == "API test description"
        assert data["is_active"] is True

    def test_get_category_endpoint(self, authenticated_client: TestClient):
        """Test GET /component-categories/{id} endpoint."""
        # First create a category
        category_data = {
            "name": "Get Test Category",
            "description": "Get test description",
            "is_active": True,
        }

        create_response = authenticated_client.post(
            "/api/v1/component-categories/",
            json=category_data,
        )
        category_id = create_response.json()["id"]

        # Then retrieve it
        response = authenticated_client.get(
            f"/api/v1/component-categories/{category_id}",
        )

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == category_id
        assert data["name"] == "Get Test Category"

    def test_list_categories_endpoint(self, authenticated_client: TestClient):
        """Test GET /component-categories/ endpoint."""
        response = authenticated_client.get(
            "/api/v1/component-categories/",
        )

        assert response.status_code == 200
        data = response.json()
        assert "categories" in data
        assert "total_count" in data
        assert "page" in data
        assert "page_size" in data

    def test_update_category_endpoint(self, authenticated_client: TestClient):
        """Test PUT /component-categories/{id} endpoint."""
        # First create a category
        category_data = {
            "name": "Update Test Category",
            "description": "Update test description",
            "is_active": True,
        }

        create_response = authenticated_client.post(
            "/api/v1/component-categories/",
            json=category_data,
        )
        category_id = create_response.json()["id"]

        # Then update it
        update_data = {
            "name": "Updated Category Name",
            "description": "Updated description",
        }

        response = authenticated_client.put(
            f"/api/v1/component-categories/{category_id}",
            json=update_data,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Category Name"
        assert data["description"] == "Updated description"

    def test_delete_category_endpoint(self, authenticated_client: TestClient):
        """Test DELETE /component-categories/{id} endpoint."""
        # First create a category
        category_data = {
            "name": "Delete Test Category",
            "description": "Delete test description",
            "is_active": True,
        }

        create_response = authenticated_client.post(
            "/api/v1/component-categories/",
            json=category_data,
        )
        category_id = create_response.json()["id"]

        # Then delete it
        response = authenticated_client.delete(
            f"/api/v1/component-categories/{category_id}",
        )

        assert response.status_code == 204

        # Verify it's deleted - should return 404
        try:
            get_response = authenticated_client.get(
                f"/api/v1/component-categories/{category_id}",
            )
            # If we get here, the request didn't raise an exception
            assert get_response.status_code == 404
        except Exception as e:
            # The test client raises an exception for 404, which is expected
            # Check that it's the right kind of exception
            assert "404" in str(e) or "not found" in str(e).lower()

    def test_get_category_tree_endpoint(self, authenticated_client: TestClient):
        """Test GET /component-categories/tree endpoint."""
        response = authenticated_client.get(
            "/api/v1/component-categories/tree",
        )

        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.text}")
        if response.status_code == 422:
            try:
                error_detail = response.json()
                print(f"Error details: {error_detail}")
            except:
                print("Could not parse error response as JSON")

        assert response.status_code == 200

    def test_move_category_endpoint(self, authenticated_client: TestClient):
        """Test PUT /component-categories/{id}/move endpoint."""
        # Create parent category
        parent_data = {
            "name": "Parent Category",
            "description": "Parent for move test",
            "is_active": True,
        }
        parent_response = authenticated_client.post(
            "/api/v1/component-categories/", json=parent_data
        )
        assert parent_response.status_code == 201
        parent_id = parent_response.json()["id"]

        # Create child category
        child_data = {
            "name": "Child Category",
            "description": "Child for move test",
            "parent_category_id": parent_id,
            "is_active": True,
        }
        child_response = authenticated_client.post(
            "/api/v1/component-categories/", json=child_data
        )
        assert child_response.status_code == 201
        child_id = child_response.json()["id"]

        # Move child to root level (null parent)
        move_response = authenticated_client.put(
            f"/api/v1/component-categories/{child_id}/move", json=None
        )
        assert move_response.status_code == 200
        moved_category = move_response.json()
        assert moved_category["parent_category_id"] is None

    def test_copy_category_endpoint(self, authenticated_client: TestClient):
        """Test POST /component-categories/{id}/copy endpoint."""
        # Create source category
        source_data = {
            "name": "Source Category",
            "description": "Source for copy test",
            "is_active": True,
        }
        source_response = authenticated_client.post(
            "/api/v1/component-categories/", json=source_data
        )
        assert source_response.status_code == 201
        source_id = source_response.json()["id"]

        # Copy category
        copy_response = authenticated_client.post(
            f"/api/v1/component-categories/{source_id}/copy",
            json={
                "target_parent_id": None,
                "copy_children": False,
                "name_suffix": " (Test Copy)",
            },
        )
        assert copy_response.status_code == 201
        copied_category = copy_response.json()
        assert copied_category["name"] == "Source Category (Test Copy)"
        assert copied_category["description"] == "Source for copy test"
        assert copied_category["id"] != source_id

    def test_restructure_categories_endpoint(self, authenticated_client: TestClient):
        """Test PUT /component-categories/restructure endpoint."""
        # Create test categories
        cat1_response = authenticated_client.post(
            "/api/v1/component-categories/",
            json={"name": "Category 1", "description": "Test 1", "is_active": True},
        )
        assert cat1_response.status_code == 201
        cat1_id = cat1_response.json()["id"]

        cat2_response = authenticated_client.post(
            "/api/v1/component-categories/",
            json={"name": "Category 2", "description": "Test 2", "is_active": True},
        )
        assert cat2_response.status_code == 201
        cat2_id = cat2_response.json()["id"]

        # Perform bulk restructure
        operations = [
            {
                "operation": "copy",
                "category_id": cat1_id,
                "target_parent_id": None,
                "copy_children": False,
                "name_suffix": " (Bulk Copy)",
            },
            {"operation": "move", "category_id": cat2_id, "new_parent_id": cat1_id},
        ]

        restructure_response = authenticated_client.put(
            "/api/v1/component-categories/restructure", json=operations
        )
        assert restructure_response.status_code == 200
        results = restructure_response.json()
        assert len(results) == 2

        # Verify copy result
        copied_category = results[0]
        assert copied_category["name"] == "Category 1 (Bulk Copy)"

        # Verify move result
        moved_category = results[1]
        assert moved_category["parent_category_id"] == cat1_id
