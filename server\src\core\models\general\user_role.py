"""User Role and Role Assignment Database Models.

This module defines SQLAlchemy models for role-based access control (RBAC) system.
It includes models for user roles, permissions, and role assignments.

Key models:
- UserRole: Represents roles that can be assigned to users
- UserRoleAssignment: Links users to roles with additional metadata
"""

import datetime
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import <PERSON>olean, DateTime, ForeignKey, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.core.models.base import Base, CommonColumns, SoftDeleteColumns
from src.core.utils.datetime_utils import utcnow_aware

if TYPE_CHECKING:
    from .user import User


class UserRole(CommonColumns, SoftDeleteColumns, Base):
    """Model representing a user role for RBAC system.

    Roles define sets of permissions that can be assigned to users.
    This supports a flexible role-based access control system.
    """

    __tablename__ = "UserRole"

    # Role properties
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    is_system_role: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Permission scope
    permissions: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True
    )  # JSON string of permissions

    # Role hierarchy support
    parent_role_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("UserRole.id"), nullable=True
    )

    # Priority for role resolution (higher number = higher priority)
    priority: Mapped[int] = mapped_column(default=0, nullable=False)

    # Self-referential relationship for role hierarchy
    parent_role: Mapped[Optional["UserRole"]] = relationship(
        "UserRole",
        remote_side="UserRole.id",
        back_populates="child_roles",
        foreign_keys=[parent_role_id],
    )

    child_roles: Mapped[List["UserRole"]] = relationship(
        "UserRole",
        back_populates="parent_role",
        foreign_keys=[parent_role_id],
    )

    # Relationships
    role_assignments: Mapped[List["UserRoleAssignment"]] = relationship(
        "UserRoleAssignment",
        back_populates="role",
        cascade="all, delete-orphan",
    )

    __table_args__ = (UniqueConstraint("name", name="uq_user_role_name"),)

    def __repr__(self) -> str:
        return (
            f"<UserRole(id={self.id}, name='{self.name}', is_active={self.is_active})>"
        )


class UserRoleAssignment(CommonColumns, SoftDeleteColumns, Base):
    """Model representing the assignment of a role to a user.

    This junction table links users to roles with additional metadata
    such as assignment dates, expiration, and assignment context.
    """

    __tablename__ = "UserRoleAssignment"

    # Foreign keys
    user_id: Mapped[int] = mapped_column(ForeignKey("User.id"), nullable=False)
    role_id: Mapped[int] = mapped_column(ForeignKey("UserRole.id"), nullable=False)

    # Assignment metadata
    assigned_by_user_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("User.id"), nullable=True
    )
    assigned_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, default=utcnow_aware, nullable=False
    )

    # Optional expiration
    expires_at: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime, nullable=True
    )

    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Context or reason for assignment
    assignment_context: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True
    )

    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        foreign_keys=[user_id],
        back_populates="role_assignments",
    )

    role: Mapped["UserRole"] = relationship(
        "UserRole",
        foreign_keys=[role_id],
        back_populates="role_assignments",
    )

    assigned_by_user: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[assigned_by_user_id],
    )

    __table_args__ = (
        UniqueConstraint("user_id", "role_id", name="uq_user_role_assignment"),
    )

    def __repr__(self) -> str:
        return (
            f"<UserRoleAssignment(id={self.id}, user_id={self.user_id}, "
            f"role_id={self.role_id}, is_active={self.is_active})>"
        )

    @property
    def is_expired(self) -> bool:
        """Check if the role assignment has expired."""
        if self.expires_at is None:
            return False
        return utcnow_aware() > self.expires_at
