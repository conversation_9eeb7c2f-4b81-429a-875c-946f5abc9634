#!/usr/bin/env python3
"""Unified Error Handling System.

This module provides a centralized error handling system that standardizes
error handling across all layers of the Ultimate Electrical Designer backend.

The unified system provides the following decorators for consistent error handling:
- `@handle_service_errors`: For service layer methods
- `@handle_repository_errors`: For repository layer methods
- `@handle_calculation_errors`: For calculation methods
- `@handle_database_errors`: For database access methods
- `@handle_api_errors`: For API endpoint methods
- `@handle_validation_errors`: For validation methods
- `@handle_security_errors`: For security-related methods
- `@handle_utility_errors`: For utility methods
- `@handle_middleware_errors`: For middleware methods
"""

import logging
import time
import traceback
from contextlib import contextmanager
from dataclasses import dataclass
from datetime import datetime, timezone
from functools import wraps
from typing import Any, Callable, Dict, Generator, List, Optional, Union

from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from sqlalchemy.exc import IntegrityError, NoResultFound, SQLAlchemyError

from src.config.logging_config import logger
from src.core.enums import ErrorContext

# Import existing error components
from src.core.errors.exceptions import (
    APIError,
    BaseApplicationException,
    DatabaseError,
    DataValidationError,
    DuplicateEntryError,
    InvalidInputError,
    MiddlewareError,
    NotFoundError,
    SecurityError,
    ServiceError,
    UtilityError,
    ValidationError,
)
from src.core.schemas.error import ErrorResponseSchema


@dataclass
class ErrorHandlingResult:
    """Result of error handling operation."""

    error_response: ErrorResponseSchema
    http_status_code: int
    should_log: bool = True
    log_level: int = logging.ERROR
    context_data: Optional[Dict[str, Any]] = None

    def __post_init__(self) -> None:
        """Initialize default values after dataclass initialization."""
        if self.context_data is None:
            self.context_data = {}

    def to_json_response(self) -> JSONResponse:
        """Convert to FastAPI JSONResponse."""
        return JSONResponse(
            status_code=self.http_status_code,
            content=self.error_response.model_dump(),
            headers={"X-Error-Handled": "unified-system"},
        )


class UnifiedErrorHandler:
    """Unified error handling system that provides consistent error handling
    across all layers of the application.
    """

    def __init__(
        self,
        enable_debug_mode: bool = False,
        include_stack_trace: bool = False,
        max_error_message_length: int = 1000,
        enable_error_tracking: bool = True,
    ):
        self.enable_debug_mode = enable_debug_mode
        self.include_stack_trace = include_stack_trace
        self.max_error_message_length = max_error_message_length
        self.enable_error_tracking = enable_error_tracking

        # Error statistics tracking
        self.error_counts: Dict[str, int] = {}
        self.error_history: List[Dict[str, Any]] = []
        self.processing_times: List[float] = []

        logger.info("Unified Error Handler initialized")

    def handle_exception(
        self,
        exception: Exception,
        context: ErrorContext = ErrorContext.API,
        request: Optional[Request] = None,
        additional_context: Optional[Dict[str, Any]] = None,
    ) -> ErrorHandlingResult:
        """Handle any exception and return standardized error response.

        Args:
            exception: The exception to handle
            context: Error context (API, service, repository, etc.)
            request: Optional FastAPI request object
            additional_context: Additional context information

        Returns:
            ErrorHandlingResult: Standardized error handling result

        """
        additional_context = additional_context or {}
        start_time = time.time()

        try:
            # Determine error type and create appropriate response
            if isinstance(exception, BaseApplicationException):
                result = self._handle_application_exception(
                    exception, context, request, additional_context
                )
            elif isinstance(exception, HTTPException):
                result = self._handle_http_exception(
                    exception, context, request, additional_context
                )
            elif isinstance(exception, SQLAlchemyError):
                result = self._handle_database_exception(
                    exception, context, request, additional_context
                )
            elif isinstance(exception, ValueError):
                result = self._handle_validation_exception(
                    exception, context, request, additional_context
                )
            else:
                result = self._handle_generic_exception(
                    exception, context, request, additional_context
                )

            # Log the error if needed
            if result.should_log:
                self._log_error(exception, result, context, additional_context)

            # Track error statistics
            if self.enable_error_tracking:
                processing_time = (
                    time.time() - start_time
                ) * 1000  # Convert to milliseconds
                self._track_error(exception, context, result, processing_time)

            return result

        except Exception as handler_error:
            # Error in error handler - create fallback response
            logger.critical(
                f"Error in unified error handler: {handler_error}", exc_info=True
            )
            return self._create_fallback_error_response(exception, handler_error)

    def _handle_application_exception(
        self,
        exception: BaseApplicationException,
        context: ErrorContext,
        request: Optional[Request],
        additional_context: Dict[str, Any],
    ) -> ErrorHandlingResult:
        """Handle custom application exceptions."""
        # Apply message length truncation if configured
        detail = exception.detail
        if (
            self.max_error_message_length
            and len(detail) > self.max_error_message_length
        ):
            detail = detail[: self.max_error_message_length]

        error_response = ErrorResponseSchema(
            code=exception.code,
            detail=detail,
            category=exception.category,
            status_code=exception.status_code,
            metadata={
                **exception.metadata,
                "context": context.value,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **additional_context,
            },
        )

        # Determine log level based on exception type
        log_level = logging.ERROR
        if isinstance(
            exception, (DataValidationError, InvalidInputError, NotFoundError)
        ):
            log_level = logging.WARNING
        elif isinstance(exception, (DatabaseError, ServiceError)):
            log_level = logging.ERROR

        return ErrorHandlingResult(
            error_response=error_response,
            http_status_code=exception.status_code,
            should_log=True,
            log_level=log_level,
            context_data={
                "exception_type": type(exception).__name__,
                "context": context.value,
                "request_path": request.url.path if request else None,
                **additional_context,
            },
        )

    def _handle_http_exception(
        self,
        exception: HTTPException,
        context: ErrorContext,
        request: Optional[Request],
        additional_context: Dict[str, Any],
    ) -> ErrorHandlingResult:
        """Handle FastAPI HTTP exceptions."""
        error_response = ErrorResponseSchema(
            code=f"HTTP_{exception.status_code}",
            detail=str(exception.detail),
            category="HTTPError",
            status_code=exception.status_code,
            metadata={
                "context": context.value,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **additional_context,
            },
        )

        return ErrorHandlingResult(
            error_response=error_response,
            http_status_code=exception.status_code,
            should_log=exception.status_code >= 500,
            log_level=(
                logging.ERROR if exception.status_code >= 500 else logging.WARNING
            ),
            context_data={
                "exception_type": "HTTPException",
                "context": context.value,
                "status_code": exception.status_code,
                **additional_context,
            },
        )

    def _handle_database_exception(
        self,
        exception: SQLAlchemyError,
        context: ErrorContext,
        request: Optional[Request],
        additional_context: Dict[str, Any],
    ) -> ErrorHandlingResult:
        """Handle database exceptions."""
        # Translate database exceptions to application exceptions
        app_exception: Union[DuplicateEntryError, NotFoundError, DatabaseError]
        if isinstance(exception, IntegrityError):
            app_exception = DuplicateEntryError(
                message="A resource with the given unique constraint already exists.",
                original_exception=exception,
            )
        elif isinstance(exception, NoResultFound):
            app_exception = NotFoundError(
                code="RESOURCE_NOT_FOUND",
                detail="The requested resource was not found.",
            )
        else:
            app_exception = DatabaseError(
                reason=f"Database operation failed: {str(exception)[:200]}",
                original_exception=exception,
            )

        # Handle the translated exception
        return self._handle_application_exception(
            app_exception, context, request, additional_context
        )

    def _handle_validation_exception(
        self,
        exception: ValueError,
        context: ErrorContext,
        request: Optional[Request],
        additional_context: Dict[str, Any],
    ) -> ErrorHandlingResult:
        """Handle validation exceptions."""
        app_exception = DataValidationError(
            details={"validation_error": str(exception)}
        )

        return self._handle_application_exception(
            app_exception, context, request, additional_context
        )

    def _handle_generic_exception(
        self,
        exception: Exception,
        context: ErrorContext,
        request: Optional[Request],
        additional_context: Dict[str, Any],
    ) -> ErrorHandlingResult:
        """Handle generic exceptions."""
        error_detail = "An unexpected internal error occurred."
        if self.enable_debug_mode:
            error_detail = (
                f"Internal error: {str(exception)[: self.max_error_message_length]}"
            )

        error_response = ErrorResponseSchema(
            code="INTERNAL_ERROR",
            detail=error_detail,
            category="ServerError",
            status_code=500,
            metadata={
                "context": context.value,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "exception_type": type(exception).__name__,
                "stack_trace": (
                    traceback.format_exc() if self.include_stack_trace else None
                ),
                **additional_context,
            },
        )

        return ErrorHandlingResult(
            error_response=error_response,
            http_status_code=500,
            should_log=True,
            log_level=logging.ERROR,
            context_data={
                "exception_type": type(exception).__name__,
                "context": context.value,
                "error_message": str(exception),
                **additional_context,
            },
        )

    def _create_fallback_error_response(
        self,
        original_exception: Exception,
        handler_error: Exception,
    ) -> ErrorHandlingResult:
        """Create fallback error response when error handler fails."""
        error_response = ErrorResponseSchema(
            code="HANDLER_ERROR",
            detail="An error occurred while processing the error response.",
            category="ServerError",
            status_code=500,
            metadata={
                "original_error": str(original_exception),
                "handler_error": str(handler_error),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
        )

        return ErrorHandlingResult(
            error_response=error_response,
            http_status_code=500,
            should_log=True,
            log_level=logging.CRITICAL,
            context_data={
                "fallback_response": True,
                "original_error": str(original_exception),
                "handler_error": str(handler_error),
            },
        )

    def _log_error(
        self,
        exception: Exception,
        result: ErrorHandlingResult,
        context: ErrorContext,
        additional_context: Dict[str, Any],
    ) -> None:
        """Log error with appropriate level and context."""
        log_message = f"[{context.value.upper()}] {result.error_response.code}: {result.error_response.detail}"

        logger.log(
            result.log_level,
            log_message,
            extra={
                "error_code": result.error_response.code,
                "error_category": result.error_response.category,
                "context": context.value,
                "context_data": result.context_data,
                "exception_type": type(exception).__name__,
            },
            exc_info=result.log_level >= logging.ERROR,
        )

    def _track_error(
        self,
        exception: Exception,
        context: ErrorContext,
        result: ErrorHandlingResult,
        processing_time_ms: float = 0.0,
    ) -> None:
        """Track error statistics for monitoring."""
        error_key = f"{context.value}:{type(exception).__name__}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1

        # Track processing times
        self.processing_times.append(processing_time_ms)
        if len(self.processing_times) > 1000:
            self.processing_times = self.processing_times[-1000:]

        # Keep limited error history
        self.error_history.append(
            {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "context": context.value,
                "exception_type": type(exception).__name__,
                "error_code": result.error_response.code,
                "status_code": result.http_status_code,
                "processing_time_ms": processing_time_ms,
                "context_data": result.context_data,
            }
        )

        # Keep only last 1000 errors
        if len(self.error_history) > 1000:
            self.error_history = self.error_history[-1000:]

    @contextmanager
    def error_context(
        self,
        context: ErrorContext,
        operation_name: str,
        additional_context: Optional[Dict[str, Any]] = None,
    ) -> Generator[None, None, None]:
        """Context manager for handling errors in specific operations.

        Usage:
            with error_handler.error_context(ErrorContext.SERVICE, "create_user"):
                # Service operation that might raise exceptions
                user_service.create_user(user_data)
        """
        additional_context = additional_context or {}
        additional_context["operation"] = operation_name

        try:
            yield
        except Exception as e:
            result = self.handle_exception(e, context, None, additional_context)
            # Re-raise as HTTPException for FastAPI
            raise HTTPException(
                status_code=result.http_status_code,
                detail=result.error_response.detail,
            )

    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics for monitoring."""
        total_errors = sum(self.error_counts.values())
        error_types = (
            list(set(error["exception_type"] for error in self.error_history))
            if self.error_history
            else []
        )

        # Calculate performance metrics
        performance_metrics = {}
        if self.processing_times:
            performance_metrics = {
                "avg_processing_time_ms": sum(self.processing_times)
                / len(self.processing_times),
                "min_processing_time_ms": min(self.processing_times),
                "max_processing_time_ms": max(self.processing_times),
                "total_processing_samples": len(self.processing_times),
            }

        return {
            "error_counts": {"total": total_errors, **self.error_counts.copy()},
            "recent_errors": self.error_history[-10:] if self.error_history else [],
            "error_types": error_types,
            "error_history_size": len(self.error_history),
            "performance_metrics": performance_metrics,
        }

    def clear_error_history(self) -> None:
        """Clear error history and statistics."""
        self.error_counts.clear()
        self.error_history.clear()
        self.processing_times.clear()
        logger.info("Error history and statistics cleared")


# Global instance for easy access
unified_error_handler = UnifiedErrorHandler()


def get_unified_error_handler() -> UnifiedErrorHandler:
    """Dependency injection function for FastAPI."""
    return unified_error_handler


# Convenience functions and decorators
def handle_service_errors(
    operation_name: str = "service_operation",
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for service methods to handle errors consistently.

    Usage:
        @handle_service_errors("create_user")
        def create_user(self, user_data):
            # Service logic here
    """
    from functools import wraps

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                result = unified_error_handler.handle_exception(
                    e,
                    ErrorContext.SERVICE,
                    additional_context={
                        "operation": operation_name,
                        "function": func.__name__,
                    },
                )
                # Convert to appropriate exception type
                if isinstance(e, BaseApplicationException):
                    raise
                else:
                    raise ServiceError(
                        f"Service operation failed: {result.error_response.detail}"
                    )

        return wrapper

    return decorator


def handle_repository_errors(
    entity_name: str = "resource",
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for repository methods to handle database errors consistently.

    Usage:
        @handle_repository_errors("user")
        def create_user(self, user_data):
            # Repository logic here
    """
    from functools import wraps

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            """Wrap repository method with error handling.

            Args:
                *args: Positional arguments
                **kwargs: Keyword arguments
            Returns:
                Any: Result of the repository method
            Raises:
                Exception: Any exception raised by the repository method

            """
            try:
                return func(*args, **kwargs)
            except Exception as e:
                result = unified_error_handler.handle_exception(
                    e,
                    ErrorContext.REPOSITORY,
                    additional_context={
                        "entity": entity_name,
                        "function": func.__name__,
                    },
                )
                # Re-raise as appropriate application exception
                if isinstance(e, BaseApplicationException):
                    raise
                elif isinstance(e, SQLAlchemyError):
                    # Already handled by unified handler, re-raise the translated exception
                    if isinstance(e, IntegrityError):
                        raise DuplicateEntryError(
                            message=f"A {entity_name} with the given unique constraint already exists.",
                            original_exception=e,
                        )
                    elif isinstance(e, NoResultFound):
                        raise NotFoundError(
                            code=f"{entity_name.upper()}_NOT_FOUND",
                            detail=f"{entity_name.capitalize()} not found.",
                        )
                    else:
                        raise DatabaseError(
                            reason=f"Database operation failed for {entity_name}",
                            original_exception=e,
                        )
                else:
                    raise ServiceError(
                        f"Repository operation failed: {result.error_response.detail}"
                    )

        return wrapper

    return decorator


def handle_calculation_errors(
    calculation_type: str = "calculation",
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for calculation methods to handle calculation errors consistently.

    Usage:
        @handle_calculation_errors("heat_loss")
        def calculate_heat_loss(self, inputs):
            # Calculation logic here
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Preserve validation errors - they should not be converted to calculation errors
                from src.core.errors.exceptions import InvalidInputError, NotFoundError

                if isinstance(e, (InvalidInputError, NotFoundError)):
                    raise  # Re-raise validation errors as-is

                result = unified_error_handler.handle_exception(
                    e,
                    ErrorContext.CALCULATION,
                    additional_context={
                        "calculation_type": calculation_type,
                        "function": func.__name__,
                    },
                )
                # Convert to calculation error
                from src.core.errors.exceptions import CalculationError

                raise CalculationError(
                    f"Calculation failed: {result.error_response.detail}"
                )

        return wrapper

    return decorator


def handle_database_errors(
    operation_name: str = "database_operation",
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for database infrastructure methods to handle database errors consistently.

    This decorator is specifically designed for core database infrastructure functions
    like engine creation, session management, and database initialization.

    Usage:
        @handle_database_errors("engine_creation")
        def create_engine(self, database_url):
            # Database infrastructure logic here
    """
    import functools

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        import asyncio
        import inspect

        if inspect.iscoroutinefunction(func):
            # Async function
            @functools.wraps(func)
            async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    result = unified_error_handler.handle_exception(
                        e,
                        ErrorContext.DATABASE,
                        additional_context={
                            "operation": operation_name,
                            "function": func.__name__,
                        },
                    )
                    # Re-raise as appropriate database exception
                    if isinstance(e, BaseApplicationException):
                        raise
                    elif isinstance(e, SQLAlchemyError):
                        # Database infrastructure errors are critical
                        raise DatabaseError(
                            reason=f"Database infrastructure operation failed: {operation_name}",
                            original_exception=e,
                        )
                    else:
                        # Other infrastructure errors
                        raise DatabaseError(
                            reason=f"Database operation failed: {result.error_response.detail}",
                            original_exception=e,
                        )

            return async_wrapper
        else:
            # Sync function
            @functools.wraps(func)
            def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    result = unified_error_handler.handle_exception(
                        e,
                        ErrorContext.DATABASE,
                        additional_context={
                            "operation": operation_name,
                            "function": func.__name__,
                        },
                    )
                    # Re-raise as appropriate database exception
                    if isinstance(e, BaseApplicationException):
                        raise
                    elif isinstance(e, SQLAlchemyError):
                        # Database infrastructure errors are critical
                        raise DatabaseError(
                            reason=f"Database infrastructure operation failed: {operation_name}",
                            original_exception=e,
                        )
                    else:
                        # Other infrastructure errors
                        raise DatabaseError(
                            reason=f"Database operation failed: {result.error_response.detail}",
                            original_exception=e,
                        )

            return sync_wrapper

    return decorator


def handle_api_errors(
    api_operation: str = "api_operation",
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for API methods to handle API errors consistently.

    Usage:
        @handle_api_errors("get_projects")
        def get_projects(self, request):
            # API logic here
    """
    import asyncio
    import inspect

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                result = func(*args, **kwargs)
                if inspect.iscoroutine(result):
                    return await result
                return result
            except Exception as e:
                # Log the error with context
                logger.error(
                    f"API operation '{api_operation}' failed: {str(e)}",
                    extra={
                        "operation": api_operation,
                        "function": func.__name__,
                        "args_count": len(args),
                        "kwargs_keys": list(kwargs.keys()),
                        "error_type": type(e).__name__,
                    },
                )

                # Re-raise as appropriate API error
                if isinstance(e, ValidationError):
                    raise e  # Pass through validation errors
                elif isinstance(e, DataValidationError):
                    raise e  # Pass through data validation errors
                elif isinstance(e, NotFoundError):
                    raise e  # Pass through not found errors
                elif isinstance(e, SecurityError):
                    raise e  # Pass through security errors
                elif isinstance(e, HTTPException):
                    raise e  # Pass through HTTP exceptions (401, 403, etc.)
                else:
                    raise APIError(
                        f"API operation '{api_operation}' failed: {str(e)}",
                        error_code=f"API_{api_operation.upper()}_ERROR",
                        context={
                            "operation": api_operation,
                            "function": func.__name__,
                            "original_error": str(e),
                        },
                    ) from e

        @wraps(func)
        def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Log the error with context
                logger.error(
                    "API operation '{}' failed: {}",
                    api_operation,
                    str(e),
                    extra={
                        "operation": api_operation,
                        "function": func.__name__,
                        "args_count": len(args),
                        "kwargs_keys": list(kwargs.keys()),
                        "error_type": type(e).__name__,
                    },
                )

                # Re-raise as appropriate API error
                if isinstance(e, ValidationError):
                    raise e  # Pass through validation errors
                elif isinstance(e, DataValidationError):
                    raise e  # Pass through data validation errors
                elif isinstance(e, NotFoundError):
                    raise e  # Pass through not found errors
                elif isinstance(e, SecurityError):
                    raise e  # Pass through security errors
                elif isinstance(e, HTTPException):
                    raise e  # Pass through HTTP exceptions (401, 403, etc.)
                else:
                    raise APIError(
                        f"API operation '{api_operation}' failed: {str(e)}",
                        error_code=f"API_{api_operation.upper()}_ERROR",
                        context={
                            "operation": api_operation,
                            "function": func.__name__,
                            "original_error": str(e),
                        },
                    ) from e

        # Return appropriate wrapper based on function type
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def handle_validation_errors(
    validation_type: str = "validation",
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for validation methods to handle validation errors consistently.

    Usage:
        @handle_validation_errors("input_validation")
        def validate_input(self, data):
            # Validation logic here
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                result = unified_error_handler.handle_exception(
                    e,
                    ErrorContext.VALIDATION,
                    additional_context={
                        "validation_type": validation_type,
                        "function": func.__name__,
                    },
                )
                # Convert to validation error
                if isinstance(e, BaseApplicationException):
                    raise
                else:
                    raise DataValidationError(
                        details={"error": result.error_response.detail}
                    )

        return wrapper

    return decorator


def handle_security_errors(
    security_operation: str = "security_check",
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for security methods to handle security errors consistently.

    Usage:
        @handle_security_errors("authentication")
        def authenticate_user(self, credentials):
            # Security logic here

        @handle_security_errors("async_authentication")
        async def async_authenticate_user(self, credentials):
            # Async security logic here
    """
    import asyncio
    import inspect

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                result = func(*args, **kwargs)
                if inspect.iscoroutine(result):
                    return await result
                return result
            except Exception as e:
                # Pass through certain exceptions that should be handled by other decorators
                if isinstance(e, DataValidationError):
                    raise e  # Pass through data validation errors
                elif isinstance(e, HTTPException):
                    raise e  # Pass through HTTP exceptions

                result = unified_error_handler.handle_exception(
                    e,
                    ErrorContext.SECURITY,
                    additional_context={
                        "security_operation": security_operation,
                        "function": func.__name__,
                    },
                )
                # Re-raise as HTTPException for security failures
                raise HTTPException(
                    status_code=result.http_status_code,
                    detail=result.error_response.detail,
                )

        @wraps(func)
        def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Pass through certain exceptions that should be handled by other decorators
                if isinstance(e, DataValidationError):
                    raise e  # Pass through data validation errors
                elif isinstance(e, HTTPException):
                    raise e  # Pass through HTTP exceptions

                result = unified_error_handler.handle_exception(
                    e,
                    ErrorContext.SECURITY,
                    additional_context={
                        "security_operation": security_operation,
                        "function": func.__name__,
                    },
                )
                # Re-raise as HTTPException for security failures
                raise HTTPException(
                    status_code=result.http_status_code,
                    detail=result.error_response.detail,
                )

        # Return appropriate wrapper based on function type
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def handle_utility_errors(
    utility_operation: str = "utility_operation",
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for utility methods to handle utility errors consistently.

    Usage:
        @handle_utility_errors("file_operation")
        def process_file(self, file_path):
            # Utility logic here
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Log the error with context
                logger.error(
                    f"Utility operation '{utility_operation}' failed: {str(e)}",
                    extra={
                        "operation": utility_operation,
                        "function": func.__name__,
                        "args_count": len(args),
                        "kwargs_keys": list(kwargs.keys()),
                        "error_type": type(e).__name__,
                    },
                )

                # Re-raise as appropriate utility error
                if isinstance(e, (FileNotFoundError, PermissionError, OSError)):
                    raise UtilityError(
                        f"Utility operation '{utility_operation}' failed: {str(e)}",
                        error_code=f"UTILITY_{utility_operation.upper()}_ERROR",
                        context={
                            "operation": utility_operation,
                            "function": func.__name__,
                            "original_error": str(e),
                        },
                    ) from e
                elif isinstance(e, (ValueError, TypeError)):
                    raise ValidationError(
                        f"Invalid input for utility operation '{utility_operation}': {str(e)}",
                        error_code=f"UTILITY_{utility_operation.upper()}_VALIDATION_ERROR",
                        context={
                            "operation": utility_operation,
                            "function": func.__name__,
                            "original_error": str(e),
                        },
                    ) from e
                else:
                    raise UtilityError(
                        f"Unexpected error in utility operation '{utility_operation}': {str(e)}",
                        error_code=f"UTILITY_{utility_operation.upper()}_UNEXPECTED_ERROR",
                        context={
                            "operation": utility_operation,
                            "function": func.__name__,
                            "original_error": str(e),
                        },
                    ) from e

        return wrapper

    return decorator


def handle_middleware_errors(
    middleware_operation: str = "middleware_operation",
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for middleware methods to handle middleware errors consistently.

    Usage:
        @handle_middleware_errors("authentication")
        def authenticate_request(self, request):
            # Middleware logic here
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Log the error with context
                logger.error(
                    "Middleware operation '{}' failed: {}",
                    middleware_operation,
                    str(e),
                    extra={
                        "operation": middleware_operation,
                        "function": func.__name__,
                        "args_count": len(args),
                        "kwargs_keys": list(kwargs.keys()),
                        "error_type": type(e).__name__,
                    },
                )

                # Re-raise as appropriate middleware error
                if isinstance(e, (PermissionError, SecurityError)):
                    raise SecurityError(
                        f"Security error in middleware operation '{middleware_operation}': {str(e)}",
                        error_code=f"MIDDLEWARE_{middleware_operation.upper()}_SECURITY_ERROR",
                        context={
                            "operation": middleware_operation,
                            "function": func.__name__,
                            "original_error": str(e),
                        },
                    ) from e
                else:
                    raise MiddlewareError(
                        f"Middleware operation '{middleware_operation}' failed: {str(e)}",
                        error_code=f"MIDDLEWARE_{middleware_operation.upper()}_ERROR",
                        context={
                            "operation": middleware_operation,
                            "function": func.__name__,
                            "original_error": str(e),
                        },
                    ) from e

        return wrapper

    return decorator


def create_error_response(
    code: str,
    detail: str,
    status_code: int = 500,
    category: str = "ServerError",
    metadata: Optional[Dict[str, Any]] = None,
) -> ErrorResponseSchema:
    """Create standardized error response.

    Args:
        code: Error code
        detail: Error detail message
        status_code: HTTP status code
        category: Error category
        metadata: Additional metadata

    Returns:
        ErrorResponseSchema: Standardized error response

    """
    return ErrorResponseSchema(
        code=code,
        detail=detail,
        category=category,
        status_code=status_code,
        metadata=metadata or {},
    )


def log_error_context(
    context: ErrorContext,
    operation: str,
    additional_data: Optional[Dict[str, Any]] = None,
) -> None:
    """Log error context information for debugging.

    Args:
        context: Error context
        operation: Operation being performed
        additional_data: Additional context data

    """
    logger.info(
        f"Error context: {context.value} - {operation}",
        extra={
            "context": context.value,
            "operation": operation,
            "additional_data": additional_data or {},
        },
    )
