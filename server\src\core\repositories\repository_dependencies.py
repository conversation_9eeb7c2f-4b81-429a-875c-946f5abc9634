# src/core/repositories/repository_dependencies.py
"""Repository Dependencies.

This module provides repository dependency injection providers for FastAPI.
Each repository receives a database session and is properly instantiated
for use in the service layer.
"""

from typing import TYPE_CHECKING

from fastapi import Depends
from sqlalchemy.orm import Session

from src.core.database.dependencies import get_db

if TYPE_CHECKING:
    from src.core.repositories.general.component_category_repository import (
        ComponentCategoryRepository,
    )
    from src.core.repositories.general.component_repository import ComponentRepository
    from src.core.repositories.general.component_type_repository import (
        ComponentTypeRepository,
    )
    from src.core.repositories.general.project_repository import ProjectRepository
    from src.core.repositories.general.user_repository import UserRepository


def get_project_repository(db: Session = Depends(get_db)) -> "ProjectRepository":
    """Dependency provider for ProjectRepository.

    Args:
        db: Database session from dependency injection

    Returns:
        ProjectRepository: Configured repository instance

    """
    from src.core.repositories.general.project_repository import ProjectRepository

    return ProjectRepository(db)


def get_user_repository(db: Session = Depends(get_db)) -> "UserRepository":
    """Dependency provider for UserRepository.

    Args:
        db: Database session from dependency injection

    Returns:
        UserRepository: Configured repository instance

    """
    from src.core.repositories.general.user_repository import UserRepository

    return UserRepository(db)


def get_component_repository(db: Session = Depends(get_db)) -> "ComponentRepository":
    """Dependency provider for ComponentRepository.

    Args:
        db: Database session from dependency injection

    Returns:
        ComponentRepository: Configured repository instance

    """
    from src.core.repositories.general.component_repository import ComponentRepository

    return ComponentRepository(db)


def get_component_category_repository(
    db: Session = Depends(get_db),
) -> "ComponentCategoryRepository":
    """Dependency provider for ComponentCategoryRepository.

    Args:
        db: Database session from dependency injection

    Returns:
        ComponentCategoryRepository: Configured repository instance

    """
    from src.core.repositories.general.component_category_repository import (
        ComponentCategoryRepository,
    )

    return ComponentCategoryRepository(db)


def get_component_type_repository(
    db: Session = Depends(get_db),
) -> "ComponentTypeRepository":
    """Dependency provider for ComponentTypeRepository.

    Args:
        db: Database session from dependency injection

    Returns:
        ComponentTypeRepository: Configured repository instance

    """
    from src.core.repositories.general.component_type_repository import (
        ComponentTypeRepository,
    )

    return ComponentTypeRepository(db)
