"""Activity Log and Audit Trail Database Models.

This module defines SQLAlchemy models for system-wide activity logging and audit trails.
It provides comprehensive tracking of user actions, system events, and data changes.

Key models:
- ActivityLog: Records all significant user actions and system events
- AuditTrail: Tracks specific data changes with before/after values
"""

import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import <PERSON>olean, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.core.enums.system_enums import ErrorSeverity
from src.core.models.base import Base, CommonColumns, EnumType
from src.core.utils.datetime_utils import utcnow_aware
from src.core.utils.json_validation import FlexibleJSON

if TYPE_CHECKING:
    from .user import User


class ActivityLog(CommonColumns, Base):
    """Model for logging all significant user actions and system events.

    This model provides comprehensive audit trail functionality that can be used
    by any module to track user activities, system events, and important operations.
    """

    __tablename__ = "ActivityLog"

    # User and session information
    user_id: Mapped[Optional[int]] = mapped_column(ForeignKey("User.id"), nullable=True)
    session_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # Action details
    action_type: Mapped[str] = mapped_column(String(100), nullable=False)
    action_description: Mapped[str] = mapped_column(Text, nullable=False)

    # Target information (what was acted upon)
    target_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    target_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    target_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # Request/Response context
    request_method: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)
    request_path: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    request_ip: Mapped[Optional[str]] = mapped_column(
        String(45), nullable=True
    )  # IPv6 support
    user_agent: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)

    # Status and outcome
    status: Mapped[str] = mapped_column(String(50), default="SUCCESS", nullable=False)
    severity: Mapped[ErrorSeverity] = mapped_column(
        EnumType(ErrorSeverity), default=ErrorSeverity.INFO, nullable=False
    )

    # Additional data and metadata
    action_metadata: Mapped[Optional[str]] = mapped_column(FlexibleJSON, nullable=True)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Performance metrics
    execution_time_ms: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)

    # Categorization
    category: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    tags: Mapped[Optional[str]] = mapped_column(
        String(500), nullable=True
    )  # Comma-separated tags

    # Audit flags
    is_security_related: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )
    is_data_change: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_system_event: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )

    # Relationships
    user: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[user_id],
        back_populates="activity_logs",
    )

    def __repr__(self) -> str:
        return (
            f"<ActivityLog(id={self.id}, action_type='{self.action_type}', "
            f"user_id={self.user_id}, status='{self.status}')>"
        )


class AuditTrail(CommonColumns, Base):
    """Model for tracking specific data changes with before/after values.

    This model provides detailed change tracking for data modifications,
    storing the exact changes made to database records.
    """

    __tablename__ = "AuditTrail"

    # Link to activity log
    activity_log_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("ActivityLog.id"), nullable=True
    )

    # User and timestamp
    user_id: Mapped[Optional[int]] = mapped_column(ForeignKey("User.id"), nullable=True)
    changed_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, default=utcnow_aware, nullable=False
    )

    # Target record information
    table_name: Mapped[str] = mapped_column(String(100), nullable=False)
    record_id: Mapped[int] = mapped_column(Integer, nullable=False)

    # Change details
    operation: Mapped[str] = mapped_column(
        String(20), nullable=False
    )  # INSERT, UPDATE, DELETE
    field_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    old_value: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    new_value: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Change metadata
    change_reason: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    change_context: Mapped[Optional[str]] = mapped_column(FlexibleJSON, nullable=True)

    # Flags
    is_sensitive: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_system_change: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )

    # Relationships
    activity_log: Mapped[Optional["ActivityLog"]] = relationship(
        "ActivityLog",
        foreign_keys=[activity_log_id],
    )

    user: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[user_id],
        back_populates="audit_trails",
    )

    def __repr__(self) -> str:
        return (
            f"<AuditTrail(id={self.id}, table_name='{self.table_name}', "
            f"record_id={self.record_id}, operation='{self.operation}')>"
        )
