'use client'

/**
 * Theme Toggle Component for Ultimate Electrical Designer
 *
 * Provides accessible theme switching with:
 * - Keyboard navigation support
 * - Screen reader compatibility (WCAG 2.1 AA)
 * - Visual indicators for current theme
 * - Integration with existing UI component patterns
 * - Support for light, dark, and system themes
 */

import { CheckIcon, MonitorIcon, MoonIcon, SunIcon } from 'lucide-react'
import * as React from 'react'

import { cn } from '../../lib/utils'
import { useUEDTheme } from '../theme-provider'
import { Button } from './button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './dropdown-menu'

/**
 * Theme option configuration
 */
interface ThemeOption {
  value: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  description: string
}

/**
 * Available theme options with icons and descriptions
 */
const THEME_OPTIONS: ThemeOption[] = [
  {
    value: 'light',
    label: 'Light',
    icon: SunIcon,
    description: 'Light theme with bright colors',
  },
  {
    value: 'dark',
    label: 'Dark',
    icon: MoonIcon,
    description: 'Dark theme with muted colors',
  },
  {
    value: 'system',
    label: 'System',
    icon: MonitorIcon,
    description: 'Follow system preference',
  },
]

/**
 * Props for the ThemeToggle component
 */
export interface ThemeToggleProps {
  /** Additional CSS classes */
  className?: string
  /** Button variant */
  variant?: 'default' | 'outline' | 'ghost' | 'secondary'
  /** Button size */
  size?: 'default' | 'sm' | 'lg' | 'icon'
  /** Whether to show the label text */
  showLabel?: boolean
  /** Custom label text */
  label?: string
  /** Whether to show theme descriptions in dropdown */
  showDescriptions?: boolean
  /** Alignment of the dropdown menu */
  align?: 'start' | 'center' | 'end'
  /** Side offset for the dropdown */
  sideOffset?: number
}

/**
 * Get the appropriate icon for the current theme
 */
function getThemeIcon(theme: string | undefined, resolvedTheme: string | undefined) {
  if (theme === 'system') {
    // For system theme, show the icon based on resolved theme
    return resolvedTheme === 'dark' ? MoonIcon : SunIcon
  }

  const option = THEME_OPTIONS.find((opt) => opt.value === theme)
  return option?.icon || SunIcon
}

/**
 * Get the display label for the current theme
 */
function getThemeLabel(theme: string | undefined) {
  const option = THEME_OPTIONS.find((opt) => opt.value === theme)
  return option?.label || 'Theme'
}

/**
 * Theme toggle component with dropdown menu
 */
export function ThemeToggle({
  className,
  variant = 'ghost',
  size = 'default',
  showLabel = false,
  label,
  showDescriptions = true,
  align = 'end',
  sideOffset = 4,
}: ThemeToggleProps) {
  const { theme, setTheme, resolvedTheme, mounted } = useUEDTheme()

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <Button
        variant={variant}
        size={size}
        className={cn('opacity-50', className)}
        disabled
        aria-label="Loading theme toggle"
      >
        <SunIcon className="size-4" />
        {showLabel && <span className="ml-2">{label || 'Theme'}</span>}
      </Button>
    )
  }

  const CurrentIcon = getThemeIcon(theme, resolvedTheme)
  const currentLabel = getThemeLabel(theme)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn('gap-2', className)}
          aria-label={`Current theme: ${currentLabel}. Click to change theme.`}
        >
          <CurrentIcon className="size-4" />
          {showLabel && <span className="ml-2">{label || currentLabel}</span>}
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align={align} sideOffset={sideOffset} className="w-48">
        {THEME_OPTIONS.map((option) => {
          const Icon = option.icon
          const isSelected = theme === option.value

          return (
            <DropdownMenuItem
              key={option.value}
              onClick={() => setTheme(option.value)}
              className={cn(
                'flex cursor-pointer items-center gap-3 px-3 py-2',
                'focus:bg-accent focus:text-accent-foreground',
                'data-[highlighted]:bg-accent data-[highlighted]:text-accent-foreground'
              )}
              aria-label={`Switch to ${option.label} theme. ${option.description}`}
            >
              <Icon className="size-4 shrink-0" />
              <div className="min-w-0 flex-1">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{option.label}</span>
                  {isSelected && (
                    <CheckIcon className="size-4 text-primary" aria-label="Currently selected" />
                  )}
                </div>
                {showDescriptions && (
                  <p className="mt-0.5 text-xs text-muted-foreground">{option.description}</p>
                )}
              </div>
            </DropdownMenuItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * Simple icon-only theme toggle button
 */
export function ThemeToggleIcon({
  className,
  variant = 'ghost',
  size = 'icon',
}: Pick<ThemeToggleProps, 'className' | 'variant' | 'size'>) {
  return (
    <ThemeToggle
      className={className}
      variant={variant}
      size={size}
      showLabel={false}
      showDescriptions={false}
    />
  )
}

/**
 * Theme toggle with label
 */
export function ThemeToggleWithLabel({
  className,
  variant = 'outline',
  size = 'default',
  label = 'Theme',
}: Pick<ThemeToggleProps, 'className' | 'variant' | 'size' | 'label'>) {
  return (
    <ThemeToggle
      className={className}
      variant={variant}
      size={size}
      showLabel={true}
      label={label}
      showDescriptions={true}
    />
  )
}

/**
 * Compact theme toggle for navigation bars
 */
export function ThemeToggleCompact({ className }: Pick<ThemeToggleProps, 'className'>) {
  return (
    <ThemeToggle
      className={className}
      variant="ghost"
      size="sm"
      showLabel={false}
      showDescriptions={false}
      align="end"
    />
  )
}

// Export the main component as default
export default ThemeToggle
