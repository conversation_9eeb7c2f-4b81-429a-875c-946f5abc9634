/**
 * React Query hooks for audit trail operations
 * Provides type-safe hooks for activity logging and audit trail management
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { auditApiClient } from '@/lib/api/audit'
import { QueryKeys, MutationKeys } from '@/types/api'
import type {
  ListQueryParams,
  ActivityLog,
  ActivityLogCreate,
  ActivityLogUpdate,
  ActivityLogFilter,
  AuditTrail,
  AuditTrailCreate,
  AuditTrailUpdate,
  AuditTrailFilter,
  AuditSummary,
  RecordHistory,
  UserActivitySummary,
} from '@/types/api'

// Activity Log Hooks
export function useActivityLogs(params?: ActivityLogFilter & ListQueryParams) {
  return useQuery({
    queryKey: QueryKeys.activityLogsList(params),
    queryFn: () => auditApiClient.getActivityLogs(params),
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

export function useActivityLog(id: number) {
  return useQuery({
    queryKey: QueryKeys.activityLog(id),
    queryFn: () => auditApiClient.getActivityLog(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useUserActivityLogs(userId: number, params?: ActivityLogFilter & ListQueryParams) {
  return useQuery({
    queryKey: QueryKeys.userActivityLogs(userId, params),
    queryFn: () => auditApiClient.getUserActivityLogs(userId, params),
    enabled: !!userId,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

export function useSecurityEvents(params?: ActivityLogFilter & ListQueryParams) {
  return useQuery({
    queryKey: QueryKeys.securityEvents(params),
    queryFn: () => auditApiClient.getSecurityEvents(params),
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

export function useCreateActivityLog() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createActivityLog,
    mutationFn: (data: ActivityLogCreate) => auditApiClient.createActivityLog(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.activityLogs })
    },
  })
}

export function useUpdateActivityLog() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.updateActivityLog,
    mutationFn: ({ id, data }: { id: number; data: ActivityLogUpdate }) =>
      auditApiClient.updateActivityLog(id, data),
    onSuccess: (updatedLog) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.activityLogs })
      queryClient.invalidateQueries({ queryKey: QueryKeys.activityLog(updatedLog.id) })
    },
  })
}

// Audit Trail Hooks
export function useAuditTrails(params?: AuditTrailFilter & ListQueryParams) {
  return useQuery({
    queryKey: QueryKeys.auditTrailsList(params),
    queryFn: () => auditApiClient.getAuditTrails(params),
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

export function useAuditTrail(id: number) {
  return useQuery({
    queryKey: QueryKeys.auditTrail(id),
    queryFn: () => auditApiClient.getAuditTrail(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useRecordHistory(tableName: string, recordId: number, params?: ListQueryParams) {
  return useQuery({
    queryKey: QueryKeys.recordHistory(tableName, recordId),
    queryFn: () => auditApiClient.getRecordHistory(tableName, recordId, params),
    enabled: !!tableName && !!recordId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useCreateAuditTrail() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createAuditTrail,
    mutationFn: (data: AuditTrailCreate) => auditApiClient.createAuditTrail(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.auditTrails })
    },
  })
}

export function useUpdateAuditTrail() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.updateAuditTrail,
    mutationFn: ({ id, data }: { id: number; data: AuditTrailUpdate }) =>
      auditApiClient.updateAuditTrail(id, data),
    onSuccess: (updatedTrail) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.auditTrails })
      queryClient.invalidateQueries({ queryKey: QueryKeys.auditTrail(updatedTrail.id) })
    },
  })
}

// Summary and Analytics Hooks
export function useAuditSummary(params?: { start_date?: string; end_date?: string }) {
  return useQuery({
    queryKey: QueryKeys.auditSummary(params),
    queryFn: () => auditApiClient.getAuditSummary(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useUserActivitySummary(
  userId: number,
  params?: { start_date?: string; end_date?: string }
) {
  return useQuery({
    queryKey: QueryKeys.userActivitySummary(userId),
    queryFn: () => auditApiClient.getUserActivitySummary(userId, params),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Maintenance Hooks
export function useCleanupAuditLogs() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.cleanupAuditLogs,
    mutationFn: (daysToKeep: number) => auditApiClient.cleanupOldLogs(daysToKeep),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.activityLogs })
      queryClient.invalidateQueries({ queryKey: QueryKeys.auditTrails })
      queryClient.invalidateQueries({ queryKey: QueryKeys.auditSummary })
    },
  })
}

// Utility Hooks
export function useLogUserActivity() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createActivityLog,
    mutationFn: (data: {
      action_type: string
      action_description: string
      target_type?: string
      target_id?: number
      target_name?: string
      category?: string
      tags?: string[]
      metadata?: Record<string, any>
    }) => auditApiClient.logUserActivity(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.activityLogs })
    },
  })
}

export function useLogSecurityEvent() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createActivityLog,
    mutationFn: (data: {
      action_type: string
      action_description: string
      severity?: 'INFO' | 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
      metadata?: Record<string, any>
    }) => auditApiClient.logSecurityEvent(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.activityLogs })
      queryClient.invalidateQueries({ queryKey: QueryKeys.securityEvents })
    },
  })
}

export function useLogDataChange() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createAuditTrail,
    mutationFn: (data: {
      table_name: string
      record_id: number
      operation: 'INSERT' | 'UPDATE' | 'DELETE'
      field_name?: string
      old_value?: string
      new_value?: string
      change_reason?: string
      activity_log_id?: number
    }) => auditApiClient.logDataChange(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.auditTrails })
    },
  })
}

export function useBatchLogActivities() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createActivityLog,
    mutationFn: (activities: ActivityLogCreate[]) => auditApiClient.batchLogActivities(activities),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.activityLogs })
    },
  })
}

export function useBatchLogDataChanges() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createAuditTrail,
    mutationFn: (changes: AuditTrailCreate[]) => auditApiClient.batchLogDataChanges(changes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.auditTrails })
    },
  })
}

// Real-time polling hooks for live updates
export function useActivityLogsPolling(
  params?: ActivityLogFilter & ListQueryParams,
  interval: number = 30000
) {
  return useQuery({
    queryKey: QueryKeys.activityLogsList(params),
    queryFn: () => auditApiClient.getActivityLogs(params),
    refetchInterval: interval,
    staleTime: 0,
  })
}

export function useSecurityEventsPolling(
  params?: ActivityLogFilter & ListQueryParams,
  interval: number = 10000
) {
  return useQuery({
    queryKey: QueryKeys.securityEvents(params),
    queryFn: () => auditApiClient.getSecurityEvents(params),
    refetchInterval: interval,
    staleTime: 0,
  })
}
