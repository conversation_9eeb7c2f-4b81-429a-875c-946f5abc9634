/**
 * @jest-environment jsdom
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, expect, it, beforeEach, afterEach } from 'vitest'
import {
  ThemeToggle,
  ThemeToggleIcon,
  ThemeToggleWithLabel,
  ThemeToggleCompact,
} from '../theme-toggle'

// Mock the theme provider
const mockSetTheme = vi.fn()
const mockUseUEDTheme = {
  theme: 'light',
  setTheme: mockSetTheme,
  themes: ['light', 'dark', 'system'],
  systemTheme: 'light',
  resolvedTheme: 'light',
  themeConfig: {
    name: 'light',
    label: 'Light',
    colors: {
      primary: '#000000',
      secondary: '#ffffff',
      background: '#ffffff',
      foreground: '#000000',
      muted: '#f5f5f5',
      accent: '#e5e5e5',
      destructive: '#ff0000',
      border: '#e5e5e5',
      input: '#ffffff',
      ring: '#000000',
    },
  },
  mounted: true,
}

vi.mock('../../../components/theme-provider', () => ({
  useUEDTheme: () => mockUseUEDTheme,
}))

// Mock UI components
vi.mock('../button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
}))

vi.mock('../dropdown-menu', () => ({
  DropdownMenu: ({ children }: any) => <div data-testid="dropdown-menu">{children}</div>,
  DropdownMenuTrigger: ({ children }: any) => <div data-testid="dropdown-trigger">{children}</div>,
  DropdownMenuContent: ({ children }: any) => <div data-testid="dropdown-content">{children}</div>,
  DropdownMenuItem: ({ children, onClick }: any) => (
    <div data-testid="dropdown-item" onClick={onClick}>
      {children}
    </div>
  ),
}))

// Mock utils
vi.mock('../../lib/utils', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
}))

describe('ThemeToggle', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly when mounted', () => {
    render(<ThemeToggle />)

    expect(screen.getByTestId('dropdown-menu')).toBeInTheDocument()
    expect(screen.getByTestId('dropdown-trigger')).toBeInTheDocument()
  })

  it('shows loading state when not mounted', () => {
    const unmountedTheme = { ...mockUseUEDTheme, mounted: false }
    vi.mocked(require('../../../components/theme-provider').useUEDTheme).mockReturnValue(
      unmountedTheme
    )

    render(<ThemeToggle />)

    expect(screen.getByRole('button')).toBeDisabled()
    expect(screen.getByLabelText('Loading theme toggle')).toBeInTheDocument()
  })

  it('displays current theme correctly', () => {
    render(<ThemeToggle showLabel={true} />)

    // Should show the current theme in the button
    expect(screen.getByText('Light')).toBeInTheDocument()
  })

  it('handles theme change when dropdown item is clicked', async () => {
    render(<ThemeToggle />)

    const dropdownItems = screen.getAllByTestId('dropdown-item')

    // Click on dark theme option (assuming it's the second item)
    if (dropdownItems.length > 1) {
      fireEvent.click(dropdownItems[1])
      expect(mockSetTheme).toHaveBeenCalled()
    }
  })

  it('shows correct accessibility labels', () => {
    render(<ThemeToggle />)

    const button = screen.getByRole('button')
    expect(button).toHaveAttribute('aria-label', expect.stringContaining('Current theme: Light'))
  })

  it('renders with custom className', () => {
    render(<ThemeToggle className="custom-class" />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('custom-class')
  })

  it('renders with different variants', () => {
    const { rerender } = render(<ThemeToggle variant="outline" />)
    expect(screen.getByRole('button')).toBeInTheDocument()

    rerender(<ThemeToggle variant="ghost" />)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('renders with different sizes', () => {
    const { rerender } = render(<ThemeToggle size="sm" />)
    expect(screen.getByRole('button')).toBeInTheDocument()

    rerender(<ThemeToggle size="lg" />)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('shows/hides descriptions based on prop', () => {
    const { rerender } = render(<ThemeToggle showDescriptions={true} />)
    expect(screen.getByTestId('dropdown-content')).toBeInTheDocument()

    rerender(<ThemeToggle showDescriptions={false} />)
    expect(screen.getByTestId('dropdown-content')).toBeInTheDocument()
  })
})

describe('ThemeToggleIcon', () => {
  it('renders icon-only version correctly', () => {
    render(<ThemeToggleIcon />)

    expect(screen.getByTestId('dropdown-menu')).toBeInTheDocument()
    expect(screen.getByRole('button')).toBeInTheDocument()
  })
})

describe('ThemeToggleWithLabel', () => {
  it('renders with label correctly', () => {
    render(<ThemeToggleWithLabel label="Custom Theme" />)

    expect(screen.getByText('Custom Theme')).toBeInTheDocument()
  })

  it('uses default label when none provided', () => {
    render(<ThemeToggleWithLabel />)

    expect(screen.getByText('Theme')).toBeInTheDocument()
  })
})

describe('ThemeToggleCompact', () => {
  it('renders compact version correctly', () => {
    render(<ThemeToggleCompact />)

    expect(screen.getByTestId('dropdown-menu')).toBeInTheDocument()
    expect(screen.getByRole('button')).toBeInTheDocument()
  })
})

describe('Theme detection', () => {
  it('shows correct icon for light theme', () => {
    const lightTheme = { ...mockUseUEDTheme, theme: 'light', resolvedTheme: 'light' }
    vi.mocked(require('../../../components/theme-provider').useUEDTheme).mockReturnValue(lightTheme)

    render(<ThemeToggle />)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('shows correct icon for dark theme', () => {
    const darkTheme = { ...mockUseUEDTheme, theme: 'dark', resolvedTheme: 'dark' }
    vi.mocked(require('../../../components/theme-provider').useUEDTheme).mockReturnValue(darkTheme)

    render(<ThemeToggle />)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('shows correct icon for system theme', () => {
    const systemTheme = { ...mockUseUEDTheme, theme: 'system', resolvedTheme: 'light' }
    vi.mocked(require('../../../components/theme-provider').useUEDTheme).mockReturnValue(
      systemTheme
    )

    render(<ThemeToggle />)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })
})

describe('Accessibility', () => {
  it('has proper ARIA labels', () => {
    render(<ThemeToggle />)

    const button = screen.getByRole('button')
    expect(button).toHaveAttribute('aria-label')
  })

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup()
    render(<ThemeToggle />)

    const button = screen.getByRole('button')
    await user.tab()
    expect(button).toHaveFocus()
  })

  it('provides screen reader text', () => {
    render(<ThemeToggle />)

    expect(screen.getByText('Toggle theme')).toBeInTheDocument()
  })
})
