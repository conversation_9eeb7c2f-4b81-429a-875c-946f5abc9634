/**
 * Audit Trail Viewer Component
 * Displays audit trail records with change history and filtering
 */

import React, { useState, useMemo } from 'react'
import { useAuditTrails, useRecordHistory } from '@/hooks/api/useAudit'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { FilterIcon, RefreshCwIcon, SearchIcon, HistoryIcon, EyeIcon } from 'lucide-react'
import { format } from 'date-fns'
import type { AuditTrail, AuditTrailFilter, RecordHistory } from '@/types/api'

interface AuditTrailViewerProps {
  userId?: number
  tableName?: string
  recordId?: number
  showFilters?: boolean
  showSearch?: boolean
  onTrailClick?: (trail: AuditTrail) => void
  className?: string
}

export function AuditTrailViewer({
  userId,
  tableName,
  recordId,
  showFilters = true,
  showSearch = true,
  onTrailClick,
  className = '',
}: AuditTrailViewerProps) {
  const [filters, setFilters] = useState<AuditTrailFilter>({
    user_id: userId,
    table_name: tableName,
    record_id: recordId,
  })
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const [selectedRecord, setSelectedRecord] = useState<{
    tableName: string
    recordId: number
  } | null>(null)

  const queryParams = useMemo(
    () => ({
      ...filters,
      skip: (currentPage - 1) * pageSize,
      limit: pageSize,
    }),
    [filters, currentPage, pageSize]
  )

  const { data: auditTrails, isLoading, error, refetch } = useAuditTrails(queryParams)
  const { data: recordHistory, isLoading: isHistoryLoading } = useRecordHistory(
    selectedRecord?.tableName || '',
    selectedRecord?.recordId || 0,
    { enabled: !!selectedRecord }
  )

  const filteredTrails = useMemo(() => {
    if (!auditTrails?.items) return []

    if (!searchTerm) return auditTrails.items

    return auditTrails.items.filter(
      (trail) =>
        trail.table_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trail.operation.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trail.field_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trail.old_value?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trail.new_value?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trail.change_reason?.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [auditTrails?.items, searchTerm])

  const getOperationColor = (operation: string): string => {
    switch (operation.toUpperCase()) {
      case 'INSERT':
        return 'bg-green-100 text-green-800 border-green-300'
      case 'UPDATE':
        return 'bg-blue-100 text-blue-800 border-blue-300'
      case 'DELETE':
        return 'bg-red-100 text-red-800 border-red-300'
      case 'SOFT_DELETE':
        return 'bg-orange-100 text-orange-800 border-orange-300'
      case 'RESTORE':
        return 'bg-purple-100 text-purple-800 border-purple-300'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  const handleFilterChange = (key: keyof AuditTrailFilter, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
    setCurrentPage(1)
  }

  const clearFilters = () => {
    setFilters({ user_id: userId, table_name: tableName, record_id: recordId })
    setSearchTerm('')
    setCurrentPage(1)
  }

  const openRecordHistory = (tableName: string, recordId: number) => {
    setSelectedRecord({ tableName, recordId })
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-red-600">Error Loading Audit Trails</CardTitle>
          <CardDescription>{error.message || 'Failed to load audit trails'}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => refetch()} variant="outline">
            <RefreshCwIcon className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Audit Trail</span>
          <Button variant="outline" size="sm" onClick={() => refetch()} disabled={isLoading}>
            <RefreshCwIcon className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
        <CardDescription>View detailed change history and data modifications</CardDescription>
      </CardHeader>

      <CardContent>
        {/* Search and Filters */}
        {(showSearch || showFilters) && (
          <div className="mb-6 space-y-4">
            {showSearch && (
              <div className="flex items-center space-x-2">
                <SearchIcon className="h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Search audit trails..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1"
                />
              </div>
            )}

            {showFilters && (
              <div className="flex flex-wrap items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <FilterIcon className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Filters:</span>
                </div>

                <Input
                  placeholder="Table name"
                  value={filters.table_name || ''}
                  onChange={(e) => handleFilterChange('table_name', e.target.value || undefined)}
                  className="w-40"
                />

                <Input
                  placeholder="Record ID"
                  type="number"
                  value={filters.record_id || ''}
                  onChange={(e) =>
                    handleFilterChange(
                      'record_id',
                      e.target.value ? parseInt(e.target.value) : undefined
                    )
                  }
                  className="w-32"
                />

                <Select
                  value={filters.operations?.[0] || ''}
                  onValueChange={(value) =>
                    handleFilterChange('operations', value ? [value] : undefined)
                  }
                >
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Operation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Operations</SelectItem>
                    <SelectItem value="INSERT">Insert</SelectItem>
                    <SelectItem value="UPDATE">Update</SelectItem>
                    <SelectItem value="DELETE">Delete</SelectItem>
                    <SelectItem value="SOFT_DELETE">Soft Delete</SelectItem>
                    <SelectItem value="RESTORE">Restore</SelectItem>
                  </SelectContent>
                </Select>

                <Input
                  placeholder="Field name"
                  value={filters.field_name || ''}
                  onChange={(e) => handleFilterChange('field_name', e.target.value || undefined)}
                  className="w-40"
                />

                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  className="text-gray-600"
                >
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Audit Trails Table */}
        <div className="overflow-hidden rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Time</TableHead>
                <TableHead>Table</TableHead>
                <TableHead>Record ID</TableHead>
                <TableHead>Operation</TableHead>
                <TableHead>Field</TableHead>
                <TableHead>Old Value</TableHead>
                <TableHead>New Value</TableHead>
                <TableHead>Reason</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={9} className="py-8 text-center">
                    <div className="flex items-center justify-center">
                      <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
                      Loading audit trails...
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredTrails.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="py-8 text-center text-gray-500">
                    No audit trails found
                  </TableCell>
                </TableRow>
              ) : (
                filteredTrails.map((trail) => (
                  <TableRow
                    key={trail.id}
                    className={`hover:bg-gray-50 ${onTrailClick ? 'cursor-pointer' : ''}`}
                    onClick={() => onTrailClick?.(trail)}
                  >
                    <TableCell className="font-mono text-sm">
                      {format(new Date(trail.changed_at), 'MMM dd, HH:mm:ss')}
                    </TableCell>
                    <TableCell>
                      <code className="rounded bg-gray-100 px-2 py-1 text-sm">
                        {trail.table_name}
                      </code>
                    </TableCell>
                    <TableCell className="font-mono">{trail.record_id}</TableCell>
                    <TableCell>
                      <Badge className={getOperationColor(trail.operation)}>
                        {trail.operation}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {trail.field_name ? (
                        <code className="rounded bg-blue-50 px-2 py-1 text-sm">
                          {trail.field_name}
                        </code>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell className="max-w-32 truncate">
                      {trail.old_value ? (
                        <code className="rounded bg-red-50 px-2 py-1 text-sm">
                          {trail.old_value}
                        </code>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell className="max-w-32 truncate">
                      {trail.new_value ? (
                        <code className="rounded bg-green-50 px-2 py-1 text-sm">
                          {trail.new_value}
                        </code>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell className="max-w-32 truncate">
                      {trail.change_reason || <span className="text-gray-400">-</span>}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          openRecordHistory(trail.table_name, trail.record_id)
                        }}
                      >
                        <HistoryIcon className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {auditTrails && auditTrails.pages > 1 && (
          <div className="mt-4 flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Showing {(currentPage - 1) * pageSize + 1} to{' '}
              {Math.min(currentPage * pageSize, auditTrails.total)} of {auditTrails.total} trails
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {currentPage} of {auditTrails.pages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((prev) => Math.min(auditTrails.pages, prev + 1))}
                disabled={currentPage === auditTrails.pages}
              >
                Next
              </Button>
            </div>
          </div>
        )}

        {/* Record History Dialog */}
        <Dialog open={!!selectedRecord} onOpenChange={() => setSelectedRecord(null)}>
          <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                Record History: {selectedRecord?.tableName} #{selectedRecord?.recordId}
              </DialogTitle>
            </DialogHeader>

            {isHistoryLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
                Loading record history...
              </div>
            ) : recordHistory ? (
              <Tabs defaultValue="timeline" className="w-full">
                <TabsList>
                  <TabsTrigger value="timeline">Timeline</TabsTrigger>
                  <TabsTrigger value="summary">Summary</TabsTrigger>
                </TabsList>

                <TabsContent value="timeline" className="space-y-4">
                  <div className="text-sm text-gray-600">
                    Total changes: {recordHistory.total_changes} | First change:{' '}
                    {format(new Date(recordHistory.first_change), 'MMM dd, yyyy HH:mm:ss')} | Last
                    change: {format(new Date(recordHistory.last_change), 'MMM dd, yyyy HH:mm:ss')}
                  </div>

                  <div className="space-y-3">
                    {recordHistory.changes.map((change, index) => (
                      <div key={change.id} className="rounded-lg border bg-gray-50 p-4">
                        <div className="mb-2 flex items-center justify-between">
                          <Badge className={getOperationColor(change.operation)}>
                            {change.operation}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            {format(new Date(change.changed_at), 'MMM dd, yyyy HH:mm:ss')}
                          </span>
                        </div>

                        {change.field_name && (
                          <div className="text-sm">
                            <strong>Field:</strong> <code>{change.field_name}</code>
                          </div>
                        )}

                        {change.old_value && (
                          <div className="text-sm">
                            <strong>Old Value:</strong>{' '}
                            <code className="rounded bg-red-50 px-2 py-1">{change.old_value}</code>
                          </div>
                        )}

                        {change.new_value && (
                          <div className="text-sm">
                            <strong>New Value:</strong>{' '}
                            <code className="rounded bg-green-50 px-2 py-1">
                              {change.new_value}
                            </code>
                          </div>
                        )}

                        {change.change_reason && (
                          <div className="text-sm">
                            <strong>Reason:</strong> {change.change_reason}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="summary" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    {Object.entries(recordHistory.change_summary).map(([operation, count]) => (
                      <div
                        key={operation}
                        className="flex items-center justify-between rounded border p-3"
                      >
                        <Badge className={getOperationColor(operation)}>{operation}</Badge>
                        <span className="text-2xl font-bold">{count}</span>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            ) : (
              <div className="py-8 text-center text-gray-500">No history found for this record</div>
            )}
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}
