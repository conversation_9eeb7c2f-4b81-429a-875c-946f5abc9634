"""User and User Preference Database Models.

This module defines SQLAlchemy models for user accounts and their preferences.
It includes models for user authentication, authorization, and application settings.

Key models:
- User: Represents user accounts with authentication and authorization details
- UserPreference: Stores user-specific application preferences and settings
"""

import datetime
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, String, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.core.models.base import (  # SoftDeleteColumns is used by UserPreference
    Base,
    CommonColumns,
    SoftDeleteColumns,
)
from src.core.utils.json_validation import FlexibleJSON

if TYPE_CHECKING:
    from .user_role import UserRoleAssignment
    from .activity_log import ActivityLog, AuditTrail


class User(CommonColumns, Base):
    """Model representing a user account.

    Users have authentication details, authorization roles,
    and preferences for the application.
    """

    __tablename__ = "User"

    email: Mapped[Optional[str]] = mapped_column(unique=True, nullable=True)
    password_hash: Mapped[Optional[str]] = mapped_column(
        nullable=True
    )  # Store hashed passwords, not plain text
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Role and permission fields for authentication/authorization
    role: Mapped[str] = mapped_column(String(50), default="user", nullable=False)
    is_admin: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # Relationships
    preferences: Mapped[Optional["UserPreference"]] = relationship(
        "UserPreference",
        back_populates="user",
        cascade="all, delete-orphan",
        uselist=False,
        foreign_keys="[UserPreference.user_id]",
    )

    # RBAC relationships
    role_assignments: Mapped[List["UserRoleAssignment"]] = relationship(
        "UserRoleAssignment",
        back_populates="user",
        cascade="all, delete-orphan",
        foreign_keys="[UserRoleAssignment.user_id]",
    )

    # Audit trail relationships
    activity_logs: Mapped[List["ActivityLog"]] = relationship(
        "ActivityLog",
        back_populates="user",
        foreign_keys="[ActivityLog.user_id]",
    )

    audit_trails: Mapped[List["AuditTrail"]] = relationship(
        "AuditTrail",
        back_populates="user",
        foreign_keys="[AuditTrail.user_id]",
    )

    __table_args__ = (UniqueConstraint("name", name="uq_user_name"),)

    def __repr__(self) -> str:
        return f"<User(id={self.id}, name='{self.name}', email='{self.email}')>"


class UserPreference(CommonColumns, SoftDeleteColumns, Base):
    """Model representing user preferences and application settings.

    User preferences include UI theme, default temperature settings,
    preferred manufacturers, and other application-specific settings.
    """

    __tablename__ = "UserPreference"

    user_id: Mapped[int] = mapped_column(
        ForeignKey("User.id"), nullable=False, unique=True
    )

    ui_theme: Mapped[str] = mapped_column(default="light", nullable=False)

    default_min_ambient_temp_c: Mapped[Optional[float]] = mapped_column(nullable=True)
    default_max_ambient_temp_c: Mapped[Optional[float]] = mapped_column(nullable=True)
    default_desired_maintenance_temp_c: Mapped[Optional[float]] = mapped_column(
        nullable=True
    )
    default_safety_margin_percent: Mapped[float] = mapped_column(
        default=0.0, nullable=False
    )

    preferred_cable_manufacturers_json: Mapped[Optional[str]] = mapped_column(
        FlexibleJSON, nullable=True
    )
    preferred_control_device_manufacturers_json: Mapped[Optional[str]] = mapped_column(
        FlexibleJSON, nullable=True
    )

    # Relationships
    user: Mapped["User"] = relationship(
        back_populates="preferences", foreign_keys=[user_id]
    )

    def __repr__(self) -> str:
        return f"<UserPreference(id={self.id}, user_id={self.user_id}, ui_theme='{self.ui_theme}')>"
