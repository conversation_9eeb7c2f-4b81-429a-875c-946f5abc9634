import { test, expect } from '@playwright/test'

test.describe('Theme System E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/')
  })

  test('should load with default theme', async ({ page }) => {
    // Check that the page loads with a theme applied
    const html = page.locator('html')
    await expect(html).toHaveAttribute('class', /h-full scroll-smooth/)

    // Check for theme-related CSS variables
    const bodyStyles = await page.evaluate(() => {
      return window.getComputedStyle(document.body)
    })

    // Should have theme-related styles applied
    expect(bodyStyles).toBeDefined()
  })

  test('should persist theme selection across page reloads', async ({ page }) => {
    // Find and click theme toggle (if available on the page)
    const themeToggle = page.getByRole('button', { name: /theme/i }).first()

    if (await themeToggle.isVisible()) {
      await themeToggle.click()

      // Select dark theme
      const darkOption = page.getByText('Dark').first()
      if (await darkOption.isVisible()) {
        await darkOption.click()

        // Wait for theme to be applied
        await page.waitForTimeout(500)

        // Reload the page
        await page.reload()

        // Check that dark theme is still applied
        const html = page.locator('html')
        await expect(html).toHaveClass(/dark/)
      }
    }
  })

  test('should apply theme changes immediately', async ({ page }) => {
    // Find theme toggle
    const themeToggle = page.getByRole('button', { name: /theme/i }).first()

    if (await themeToggle.isVisible()) {
      // Get initial background color
      const initialBg = await page.evaluate(() => {
        return window.getComputedStyle(document.body).backgroundColor
      })

      await themeToggle.click()

      // Select different theme
      const darkOption = page.getByText('Dark').first()
      if (await darkOption.isVisible()) {
        await darkOption.click()

        // Wait for theme change
        await page.waitForTimeout(500)

        // Get new background color
        const newBg = await page.evaluate(() => {
          return window.getComputedStyle(document.body).backgroundColor
        })

        // Background should have changed
        expect(newBg).not.toBe(initialBg)
      }
    }
  })

  test('should handle system theme preference', async ({ page }) => {
    // Set system to dark mode
    await page.emulateMedia({ colorScheme: 'dark' })

    const themeToggle = page.getByRole('button', { name: /theme/i }).first()

    if (await themeToggle.isVisible()) {
      await themeToggle.click()

      // Select system theme
      const systemOption = page.getByText('System').first()
      if (await systemOption.isVisible()) {
        await systemOption.click()

        // Wait for theme to be applied
        await page.waitForTimeout(500)

        // Should apply dark theme based on system preference
        const html = page.locator('html')
        await expect(html).toHaveClass(/dark/)
      }
    }
  })

  test('should be accessible with keyboard navigation', async ({ page }) => {
    // Find theme toggle
    const themeToggle = page.getByRole('button', { name: /theme/i }).first()

    if (await themeToggle.isVisible()) {
      // Focus the theme toggle with keyboard
      await themeToggle.focus()
      await expect(themeToggle).toBeFocused()

      // Open dropdown with Enter key
      await page.keyboard.press('Enter')

      // Navigate through options with arrow keys
      await page.keyboard.press('ArrowDown')
      await page.keyboard.press('ArrowDown')

      // Select option with Enter
      await page.keyboard.press('Enter')

      // Theme should change
      await page.waitForTimeout(500)
    }
  })

  test('should show correct theme indicators', async ({ page }) => {
    const themeToggle = page.getByRole('button', { name: /theme/i }).first()

    if (await themeToggle.isVisible()) {
      await themeToggle.click()

      // Should show current theme with checkmark or indicator
      const currentThemeIndicator = page.locator('[aria-label*="Currently selected"]').first()
      if (await currentThemeIndicator.isVisible()) {
        await expect(currentThemeIndicator).toBeVisible()
      }
    }
  })

  test('should work in settings page', async ({ page }) => {
    // Navigate to settings page (if it exists)
    try {
      await page.goto('/settings')

      // Look for appearance or theme section
      const appearanceSection = page.getByText('Appearance').first()
      if (await appearanceSection.isVisible()) {
        await appearanceSection.click()

        // Should find theme field
        const themeField = page.getByText('Color Theme').first()
        await expect(themeField).toBeVisible()

        // Should have theme toggle component
        const themeToggle = page.getByRole('button', { name: /change theme/i }).first()
        if (await themeToggle.isVisible()) {
          await expect(themeToggle).toBeVisible()
        }
      }
    } catch (error) {
      // Settings page might not exist yet, skip this test
      test.skip()
    }
  })

  test('should handle theme errors gracefully', async ({ page }) => {
    // Inject an error into the theme system
    await page.addInitScript(() => {
      // Mock localStorage to throw an error
      const originalSetItem = localStorage.setItem
      localStorage.setItem = function (key: string, value: string) {
        if (key.includes('theme')) {
          throw new Error('Storage error')
        }
        return originalSetItem.call(this, key, value)
      }
    })

    // Page should still load and function
    await page.reload()

    // Should have fallback theme applied
    const body = page.locator('body')
    await expect(body).toBeVisible()
  })

  test('should support theme transitions', async ({ page }) => {
    const themeToggle = page.getByRole('button', { name: /theme/i }).first()

    if (await themeToggle.isVisible()) {
      // Check if transitions are disabled during theme changes
      await themeToggle.click()

      const darkOption = page.getByText('Dark').first()
      if (await darkOption.isVisible()) {
        await darkOption.click()

        // Check that transition classes are applied/removed appropriately
        await page.waitForTimeout(100)

        // Theme should be applied smoothly
        const html = page.locator('html')
        await expect(html).toBeVisible()
      }
    }
  })

  test('should maintain theme across different pages', async ({ page }) => {
    const themeToggle = page.getByRole('button', { name: /theme/i }).first()

    if (await themeToggle.isVisible()) {
      await themeToggle.click()

      const darkOption = page.getByText('Dark').first()
      if (await darkOption.isVisible()) {
        await darkOption.click()
        await page.waitForTimeout(500)

        // Navigate to different page
        try {
          await page.goto('/dashboard')

          // Theme should persist
          const html = page.locator('html')
          await expect(html).toHaveClass(/dark/)
        } catch (error) {
          // Dashboard might not exist, try home page
          await page.goto('/')

          const html = page.locator('html')
          await expect(html).toHaveClass(/dark/)
        }
      }
    }
  })
})
