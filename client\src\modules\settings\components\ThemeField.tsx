/**
 * Theme Field Component for Settings
 * Ultimate Electrical Designer - Settings & User Preferences
 *
 * Custom theme field that integrates the ThemeToggle component
 * with the settings form system and user preferences API
 */

'use client'

import { Info } from 'lucide-react'
import { useEffect } from 'react'
import { useUEDTheme } from '../../../components/theme-provider'
import { Alert, AlertDescription } from '../../../components/ui/alert'
import { Label } from '../../../components/ui/label'
import { ThemeToggleWithLabel } from '../../../components/ui/theme-toggle'
import type { SettingsField } from '../types'

interface ThemeFieldProps {
  field: SettingsField
  currentValue: string
  hasError: boolean
  errorMessage?: string
  onFieldChange: (fieldId: string, value: any) => void
  className?: string
}

/**
 * Theme Field Component
 * Integrates the ThemeToggle component with the settings form
 */
export function ThemeField({
  field,
  currentValue,
  hasError,
  errorMessage,
  onFieldChange,
  className = '',
}: ThemeFieldProps) {
  const { theme, setTheme, resolvedTheme, mounted } = useUEDTheme()
  const fieldId = `setting-field-${field.id}`

  // Sync theme changes with settings form
  useEffect(() => {
    if (mounted && theme && theme !== currentValue) {
      onFieldChange(field.id, theme)
    }
  }, [theme, currentValue, field.id, onFieldChange, mounted])

  // Handle theme change from settings form
  const handleThemeChange = (newTheme: string) => {
    setTheme(newTheme)
    onFieldChange(field.id, newTheme)
  }

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <div className={`space-y-2 ${className}`}>
        <Label htmlFor={fieldId} className="text-sm font-medium">
          {field.label}
          {field.validation?.required && <span className="ml-1 text-destructive">*</span>}
        </Label>
        <div className="flex items-center space-x-3 opacity-50">
          <div className="h-9 w-32 animate-pulse rounded-md bg-muted" />
          <span className="text-sm text-muted-foreground">Loading...</span>
        </div>
        {field.description && <p className="text-xs text-muted-foreground">{field.description}</p>}
      </div>
    )
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={fieldId} className="text-sm font-medium">
        {field.label}
        {field.validation?.required && <span className="ml-1 text-destructive">*</span>}
      </Label>

      <div className="flex items-center space-x-3">
        <ThemeToggleWithLabel
          variant="outline"
          size="default"
          label="Change Theme"
          className={hasError ? 'border-destructive' : ''}
        />

        {/* Current theme indicator */}
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <span>Current:</span>
          <span className="font-medium capitalize">
            {theme === 'system' ? `System (${resolvedTheme})` : theme}
          </span>
        </div>
      </div>

      {/* Theme preview info */}
      <Alert className="border-muted">
        <Info className="h-4 w-4" />
        <AlertDescription className="text-xs">
          {theme === 'system'
            ? 'Automatically switches between light and dark based on your system preference.'
            : theme === 'dark'
              ? 'Dark theme with muted colors for reduced eye strain.'
              : 'Light theme with bright colors for better visibility.'}
        </AlertDescription>
      </Alert>

      {field.description && <p className="text-xs text-muted-foreground">{field.description}</p>}

      {hasError && errorMessage && (
        <p className="text-xs text-destructive" role="alert">
          {errorMessage}
        </p>
      )}
    </div>
  )
}

/**
 * Hook to check if a field should use the custom theme field
 */
export function useIsThemeField(field: SettingsField): boolean {
  return field.id === 'theme' && field.type === 'select'
}

export default ThemeField
