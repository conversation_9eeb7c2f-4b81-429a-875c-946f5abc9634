#!/usr/bin/env python3
"""Comprehensive integration tests for Component Management API workflow."""

import pytest
import json
from decimal import Decimal
from fastapi.testclient import TestClient

from src.core.schemas.general.component_category_schemas import (
    ComponentCategoryCreateSchema,
)
from src.core.schemas.general.component_type_schemas import ComponentTypeCreateSchema
from src.core.schemas.general.component_schemas import ComponentCreateSchema
from src.core.enums.electrical_enums import ComponentType, ComponentCategoryType


class TestComponentManagementWorkflow:
    """Test complete workflow: ComponentCategory → ComponentType → Component relationships."""

    def test_complete_workflow_with_relational_fields(
        self, authenticated_client: TestClient
    ):
        """Test complete workflow using new relational approach."""

        # Step 1: Create a ComponentCategory
        category_data = {
            "name": "Test Protection Devices",
            "description": "Test category for protection devices",
            "is_active": True,
        }

        category_response = authenticated_client.post(
            "/api/v1/component-categories/", json=category_data
        )
        assert category_response.status_code == 201
        category = category_response.json()
        category_id = category["id"]

        # Step 2: Create a ComponentType in that category
        type_data = {
            "name": "Test Circuit Breaker Type",
            "description": "Test type for circuit breakers",
            "category_id": category_id,
            "is_active": True,
            "specifications_template": {
                "electrical": {
                    "current_rating": {"type": "string", "required": True},
                    "voltage_rating": {"type": "string", "required": True},
                }
            },
        }

        type_response = authenticated_client.post(
            "/api/v1/component-types/", json=type_data
        )
        assert type_response.status_code == 201
        component_type = type_response.json()
        type_id = component_type["id"]

        # Step 3: Create a Component using the new relational fields
        component_data = {
            "name": "Test Circuit Breaker",
            "manufacturer": "Test Manufacturer",
            "model_number": "TCB-001",
            "description": "Test circuit breaker component",
            "component_type_id": type_id,
            "component_category_id": category_id,
            "specifications": {
                "electrical": {"current_rating": "16A", "voltage_rating": "230V"}
            },
            "unit_price": 25.50,
            "currency": "USD",
            "supplier": "Test Supplier",
            "part_number": "TCB-001-TS",
            "is_active": True,
            "is_preferred": False,
            "stock_status": "available",
        }

        component_response = authenticated_client.post(
            "/api/v1/components/", json=component_data
        )
        assert component_response.status_code == 201
        component = component_response.json()

        # Verify the component was created with correct relationships
        assert component["name"] == "Test Circuit Breaker"
        assert component["manufacturer"] == "Test Manufacturer"
        assert component["model_number"] == "TCB-001"

        # Step 4: Verify data consistency by retrieving the component
        component_get_response = authenticated_client.get(
            f"/api/v1/components/{component['id']}"
        )
        assert component_get_response.status_code == 200
        retrieved_component = component_get_response.json()

        assert retrieved_component["id"] == component["id"]
        assert retrieved_component["name"] == component_data["name"]

    def test_backward_compatibility_with_legacy_enums(
        self, authenticated_client: TestClient
    ):
        """Test that legacy enum fields still work for backward compatibility."""

        component_data = {
            "name": "Legacy Circuit Breaker",
            "manufacturer": "Legacy Manufacturer",
            "model_number": "LCB-001",
            "description": "Legacy circuit breaker component",
            "component_type": "Circuit Breaker",
            "category": "Protection Devices",
            "specifications": {
                "electrical": {"current_rating": "20A", "voltage_rating": "400V"}
            },
            "unit_price": 35.75,
            "currency": "EUR",
            "supplier": "Legacy Supplier",
            "part_number": "LCB-001-LS",
            "is_active": True,
            "is_preferred": True,
            "stock_status": "available",
        }

        component_response = authenticated_client.post(
            "/api/v1/components/", json=component_data
        )
        assert component_response.status_code == 201
        component = component_response.json()

        # Verify the component was created successfully
        assert component["name"] == "Legacy Circuit Breaker"
        assert component["manufacturer"] == "Legacy Manufacturer"
        assert component["model_number"] == "LCB-001"

    def test_component_type_category_relationship_validation(
        self, authenticated_client: TestClient
    ):
        """Test validation of ComponentType and ComponentCategory relationships."""

        # Try to create a ComponentType with non-existent category
        invalid_type_data = {
            "name": "Invalid Type",
            "description": "Type with invalid category",
            "category_id": 99999,  # Non-existent category
            "is_active": True,
        }

        try:
            type_response = authenticated_client.post(
                "/api/v1/component-types/", json=invalid_type_data
            )
            # Should fail with validation error
            assert type_response.status_code in [400, 422]
        except Exception as e:
            # The validation error is being raised as an exception, which is expected
            assert "Category does not exist or is inactive" in str(e)

    def test_component_creation_validation_errors(
        self, authenticated_client: TestClient
    ):
        """Test various validation error scenarios for component creation."""

        # Test 1: Component without any classification
        invalid_component_data = {
            "name": "Invalid Component",
            "manufacturer": "Test Manufacturer",
            "model_number": "INV-001",
            "description": "Component without classification",
            "unit_price": 25.00,
            "currency": "USD",
            "supplier": "Test Supplier",
            "part_number": "INV-001-TS",
            "is_active": True,
        }

        response = authenticated_client.post(
            "/api/v1/components/", json=invalid_component_data
        )
        # Should fail validation
        assert response.status_code in [400, 422]

        # Test 2: Component with invalid component_type_id
        invalid_component_data_2 = {
            "name": "Invalid Component 2",
            "manufacturer": "Test Manufacturer",
            "model_number": "INV-002",
            "description": "Component with invalid type ID",
            "component_type_id": 99999,  # Non-existent type
            "unit_price": 25.00,
            "currency": "USD",
            "supplier": "Test Supplier",
            "part_number": "INV-002-TS",
            "is_active": True,
        }

        response_2 = authenticated_client.post(
            "/api/v1/components/", json=invalid_component_data_2
        )
        # Should fail validation
        assert response_2.status_code in [400, 422]

    def test_component_search_with_relational_fields(
        self, authenticated_client: TestClient
    ):
        """Test component search functionality with new relational fields."""

        # First create the necessary data structure
        category_data = {
            "name": "Search Test Category",
            "description": "Category for search testing",
            "is_active": True,
        }

        category_response = authenticated_client.post(
            "/api/v1/component-categories/", json=category_data
        )
        assert category_response.status_code == 201
        category_id = category_response.json()["id"]

        type_data = {
            "name": "Search Test Type",
            "description": "Type for search testing",
            "category_id": category_id,
            "is_active": True,
        }

        type_response = authenticated_client.post(
            "/api/v1/component-types/", json=type_data
        )
        assert type_response.status_code == 201
        type_id = type_response.json()["id"]

        # Create a component for searching
        component_data = {
            "name": "Searchable Component",
            "manufacturer": "Search Manufacturer",
            "model_number": "SEARCH-001",
            "description": "Component for search testing",
            "component_type_id": type_id,
            "component_category_id": category_id,
            "unit_price": 45.00,
            "currency": "USD",
            "supplier": "Search Supplier",
            "part_number": "SEARCH-001-SS",
            "is_active": True,
        }

        component_response = authenticated_client.post(
            "/api/v1/components/", json=component_data
        )
        assert component_response.status_code == 201

        # Test search functionality
        search_response = authenticated_client.get(
            "/api/v1/components/?search_term=Searchable"
        )
        assert search_response.status_code == 200
        search_results = search_response.json()

        # Verify search found our component
        assert len(search_results["items"]) >= 1
        found_component = next(
            (
                item
                for item in search_results["items"]
                if item["name"] == "Searchable Component"
            ),
            None,
        )
        assert found_component is not None
        assert found_component["manufacturer"] == "Search Manufacturer"

    def test_component_update_with_relational_fields(
        self, authenticated_client: TestClient
    ):
        """Test updating components using new relational fields."""

        # Create initial data structure
        category_data = {
            "name": "Update Test Category",
            "description": "Category for update testing",
            "is_active": True,
        }

        category_response = authenticated_client.post(
            "/api/v1/component-categories/", json=category_data
        )
        assert category_response.status_code == 201
        category_id = category_response.json()["id"]

        type_data = {
            "name": "Update Test Type",
            "description": "Type for update testing",
            "category_id": category_id,
            "is_active": True,
        }

        type_response = authenticated_client.post(
            "/api/v1/component-types/", json=type_data
        )
        assert type_response.status_code == 201
        type_id = type_response.json()["id"]

        # Create a component
        component_data = {
            "name": "Updatable Component",
            "manufacturer": "Update Manufacturer",
            "model_number": "UPD-001",
            "description": "Component for update testing",
            "component_type_id": type_id,
            "component_category_id": category_id,
            "unit_price": 55.00,
            "currency": "USD",
            "supplier": "Update Supplier",
            "part_number": "UPD-001-US",
            "is_active": True,
        }

        component_response = authenticated_client.post(
            "/api/v1/components/", json=component_data
        )
        assert component_response.status_code == 201
        component_id = component_response.json()["id"]

        # Update the component
        update_data = {
            "name": "Updated Component Name",
            "unit_price": 65.00,
            "description": "Updated description for testing",
        }

        update_response = authenticated_client.put(
            f"/api/v1/components/{component_id}", json=update_data
        )
        assert update_response.status_code == 200
        updated_component = update_response.json()

        # Verify the update
        assert updated_component["name"] == "Updated Component Name"
        assert float(updated_component["unit_price"]) == 65.00
        assert updated_component["description"] == "Updated description for testing"
        # Original fields should remain unchanged
        assert updated_component["manufacturer"] == "Update Manufacturer"
        assert updated_component["model_number"] == "UPD-001"
