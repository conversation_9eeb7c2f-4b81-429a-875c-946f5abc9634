/**
 * Preferences Form Component
 * Ultimate Electrical Designer - Settings & User Preferences
 */

'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { AlertTriangle, Info } from 'lucide-react'
import { SETTINGS_CATEGORIES } from '../constants'
import { useSettings } from '../hooks/useSettings'
import type { SettingsCategory, UserPreferences } from '../types'
import { formatPreferenceValue } from '../utils'

interface PreferencesFormProps {
  category: SettingsCategory
  className?: string
}

/**
 * Preferences Form Component
 */
export function PreferencesForm({ category, className = '' }: PreferencesFormProps) {
  const settings = useSettings()
  const { preferences, formData, ui, actions } = settings

  const categoryConfig = SETTINGS_CATEGORIES.find((cat) => cat.id === category)

  if (!categoryConfig) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>Category configuration not found: {category}</AlertDescription>
      </Alert>
    )
  }

  // Get current value for a field
  const getCurrentValue = (fieldId: string) => {
    if (fieldId in formData) {
      return formData[fieldId as keyof UserPreferences]
    }
    return preferences?.[fieldId as keyof UserPreferences]
  }

  // Handle field change
  const handleFieldChange = (fieldId: string, value: any) => {
    actions.updateField(fieldId as keyof UserPreferences, value)
    actions.clearFieldError(fieldId)
  }

  // Render field based on type
  const renderField = (field: any) => {
    const currentValue = getCurrentValue(field.id)
    const hasError = ui.errors[field.id]
    const fieldId = `setting-field-${field.id}`

    // Use custom theme field for theme settings
    if (field.id === 'theme' && field.type === 'select') {
      // Import ThemeField dynamically to avoid import issues
      const { ThemeField } = require('./ThemeField')
      return (
        <ThemeField
          key={field.id}
          field={field}
          currentValue={currentValue}
          hasError={hasError}
          errorMessage={ui.errors[field.id]}
          onFieldChange={handleFieldChange}
        />
      )
    }

    switch (field.type) {
      case 'text':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={fieldId} className="text-sm font-medium">
              {field.label}
              {field.validation?.required && <span className="ml-1 text-destructive">*</span>}
            </Label>
            <Input
              id={fieldId}
              type="text"
              value={currentValue || ''}
              onChange={(e) => handleFieldChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              disabled={field.disabled}
              className={hasError ? 'border-destructive' : ''}
              aria-describedby={field.description ? `${fieldId}-description` : undefined}
              aria-invalid={hasError ? 'true' : 'false'}
            />
            {field.description && (
              <p id={`${fieldId}-description`} className="text-xs text-muted-foreground">
                {field.description}
              </p>
            )}
            {hasError && (
              <p className="text-xs text-destructive" role="alert">
                {ui.errors[field.id]}
              </p>
            )}
          </div>
        )

      case 'number':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={fieldId} className="text-sm font-medium">
              {field.label}
              {field.validation?.required && <span className="ml-1 text-destructive">*</span>}
            </Label>
            <Input
              id={fieldId}
              type="number"
              value={currentValue || ''}
              onChange={(e) => handleFieldChange(field.id, Number(e.target.value))}
              min={field.validation?.min}
              max={field.validation?.max}
              disabled={field.disabled}
              className={hasError ? 'border-destructive' : ''}
              aria-describedby={field.description ? `${fieldId}-description` : undefined}
              aria-invalid={hasError ? 'true' : 'false'}
            />
            {field.description && (
              <p id={`${fieldId}-description`} className="text-xs text-muted-foreground">
                {field.description}
              </p>
            )}
            {hasError && (
              <p className="text-xs text-destructive" role="alert">
                {ui.errors[field.id]}
              </p>
            )}
          </div>
        )

      case 'boolean':
        return (
          <div key={field.id} className="flex items-center justify-between space-x-2">
            <div className="space-y-0.5">
              <Label htmlFor={fieldId} className="text-sm font-medium">
                {field.label}
              </Label>
              {field.description && (
                <p className="text-xs text-muted-foreground">{field.description}</p>
              )}
            </div>
            <Switch
              id={fieldId}
              checked={currentValue || false}
              onCheckedChange={(checked) => handleFieldChange(field.id, checked)}
              disabled={field.disabled}
              aria-describedby={field.description ? `${fieldId}-description` : undefined}
            />
          </div>
        )

      case 'select':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={fieldId} className="text-sm font-medium">
              {field.label}
              {field.validation?.required && <span className="ml-1 text-destructive">*</span>}
            </Label>
            <Select
              value={currentValue || ''}
              onValueChange={(value) => handleFieldChange(field.id, value)}
              disabled={field.disabled}
            >
              <SelectTrigger id={fieldId} className={hasError ? 'border-destructive' : ''}>
                <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option: any) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {field.description && (
              <p className="text-xs text-muted-foreground">{field.description}</p>
            )}
            {hasError && (
              <p className="text-xs text-destructive" role="alert">
                {ui.errors[field.id]}
              </p>
            )}
          </div>
        )

      case 'slider':
        return (
          <div key={field.id} className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor={fieldId} className="text-sm font-medium">
                {field.label}
              </Label>
              <Badge variant="secondary" className="text-xs">
                {currentValue}
              </Badge>
            </div>
            <Slider
              id={fieldId}
              value={[currentValue || field.validation?.min || 0]}
              onValueChange={(values) => handleFieldChange(field.id, values[0])}
              min={field.validation?.min}
              max={field.validation?.max}
              step={field.step || 1}
              disabled={field.disabled}
              className="w-full"
            />
            {field.description && (
              <p className="text-xs text-muted-foreground">{field.description}</p>
            )}
            {hasError && (
              <p className="text-xs text-destructive" role="alert">
                {ui.errors[field.id]}
              </p>
            )}
          </div>
        )

      default:
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-sm font-medium">{field.label}</Label>
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>Unsupported field type: {field.type}</AlertDescription>
            </Alert>
          </div>
        )
    }
  }

  return (
    <div className={`preferences-form space-y-6 ${className}`}>
      {categoryConfig.sections.map((section, sectionIndex) => (
        <div key={section.id} className="space-y-4">
          {/* Section Header */}
          <div>
            <h3 className="text-lg font-medium">{section.label}</h3>
            {section.description && (
              <p className="mt-1 text-sm text-muted-foreground">{section.description}</p>
            )}
          </div>

          {/* Section Fields */}
          <div className="space-y-4">{section.fields.map((field) => renderField(field))}</div>

          {/* Section Separator */}
          {sectionIndex < categoryConfig.sections.length - 1 && <Separator className="my-6" />}
        </div>
      ))}

      {/* Category-specific additional content */}
      {category === 'appearance' && (
        <div className="mt-6 rounded-lg bg-muted p-4">
          <h4 className="mb-2 text-sm font-medium">Theme Preview</h4>
          <div className="flex gap-2">
            <div className="h-8 w-8 rounded border bg-background"></div>
            <div className="h-8 w-8 rounded bg-primary"></div>
            <div className="h-8 w-8 rounded bg-secondary"></div>
            <div className="h-8 w-8 rounded bg-accent"></div>
          </div>
        </div>
      )}

      {category === 'engineering' && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Engineering settings affect calculation accuracy and compliance. Changes may require
            recalculation of existing projects.
          </AlertDescription>
        </Alert>
      )}

      {category === 'advanced' && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Advanced settings are intended for experienced users. Incorrect values may affect
            application performance.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}

/**
 * Quick Settings Form for specific fields
 */
export function QuickSettingsForm({
  fields,
  className = '',
}: {
  fields: string[]
  className?: string
}) {
  const settings = useSettings()
  const { preferences, formData, ui, actions } = settings

  // Find fields across all categories
  const fieldConfigs = SETTINGS_CATEGORIES.flatMap((category) =>
    category.sections.flatMap((section) =>
      section.fields.filter((field) => fields.includes(field.id))
    )
  )

  if (fieldConfigs.length === 0) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>No matching fields found: {fields.join(', ')}</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className={`quick-settings-form space-y-4 ${className}`}>
      {fieldConfigs.map((field) => {
        // Use the same rendering logic as PreferencesForm
        // This is a simplified version for demonstration
        const currentValue =
          field.id in formData
            ? formData[field.id as keyof UserPreferences]
            : preferences?.[field.id as keyof UserPreferences]

        return (
          <div key={field.id} className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">{field.label}</Label>
              {field.description && (
                <p className="text-xs text-muted-foreground">{field.description}</p>
              )}
            </div>
            <div className="text-sm text-muted-foreground">
              {formatPreferenceValue(field.id as keyof UserPreferences, currentValue)}
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default PreferencesForm
