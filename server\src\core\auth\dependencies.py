"""Authentication Dependencies for Ultimate Electrical Designer.

This module provides authentication dependencies that are used across
the application to ensure proper authentication and authorization.
"""

# Re-export authentication dependencies from the security module
from src.core.security.enhanced_dependencies import (
    get_current_user,
    get_admin_user,
    get_active_user,
    get_optional_user,
    require_authenticated_user,
    require_admin_user,
)

__all__ = [
    "get_current_user",
    "get_admin_user",
    "get_active_user",
    "get_optional_user",
    "require_authenticated_user",
    "require_admin_user",
]
