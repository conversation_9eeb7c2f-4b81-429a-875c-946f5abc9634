# tests/conftest.py - Global test configuration
import os
import sys
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient

# Set testing environment variables before importing application modules
os.environ["TESTING"] = "true"
os.environ["ENVIRONMENT"] = "testing"

# Add server and tests to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..")
tests_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, server_path)
sys.path.insert(0, tests_path)

from src.core.database.dependencies import get_db
from src.core.models.base import Base

# Test database configuration
TEST_DATABASE_URL = "sqlite:///./test_app.db"


@pytest.fixture(scope="session")
def test_settings():
    """Override settings for tests."""
    from src.config.settings import settings

    settings.DATABASE_URL = TEST_DATABASE_URL
    settings.ENVIRONMENT = "testing"
    return settings


@pytest.fixture(scope="session")
def engine(test_settings):
    """Create a test database engine for the session."""
    engine = create_engine(
        test_settings.effective_database_url,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def db_session(engine) -> Generator[Session, None, None]:
    """Create a new database session for each test."""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        with engine.connect() as conn:
            trans = conn.begin()
            for table in reversed(Base.metadata.sorted_tables):
                conn.execute(table.delete())
            trans.commit()


@pytest.fixture(scope="function")
def client(db_session: Session, engine) -> Generator[TestClient, None, None]:
    """Create a test client with the database session override."""
    from src.app import create_app

    app = create_app(db_engine=engine)

    def override_get_db():
        yield db_session

    app.dependency_overrides[get_db] = override_get_db

    with TestClient(app) as test_client:
        yield test_client

    app.dependency_overrides.clear()


# --------------------------------User Fixtures--------------------------------#

import uuid

unique_suffix = str(uuid.uuid4())[:8]


@pytest.fixture
def admin_user_data():
    """Create admin user data."""
    from src.core.schemas.general.user_schemas import UserRole

    return {
        "name": f"Test Admin {unique_suffix}",
        "email": f"admin.{unique_suffix}@example.com",
        "password": "SecurePass123",
        "role": UserRole.ADMIN,
        "is_active": True,
    }


@pytest.fixture
def test_admin_user(db_session, admin_user_data):
    """Create an admin user in the database."""
    from src.core.services.general.user_service import UserService
    from src.core.schemas.general.user_schemas import UserCreateSchema

    user_service = UserService(db_session)
    user_create = UserCreateSchema(**admin_user_data)
    user = user_service.create_user(user_create)
    return user


@pytest.fixture
def test_user_data():
    """Create test user data."""
    from src.core.schemas.general.user_schemas import UserRole

    return {
        "name": f"Test Viewer {unique_suffix}",
        "email": f"viewer.{unique_suffix}@example.com",
        "password": "SecurePass123",
        "role": UserRole.VIEWER,
        "is_active": True,
    }


@pytest.fixture
def test_user(db_session, test_user_data):
    """Create a test user in the database."""
    from src.core.services.general.user_service import UserService
    from src.core.schemas.general.user_schemas import UserCreateSchema

    user_service = UserService(db_session)
    user_create = UserCreateSchema(**test_user_data)
    user = user_service.create_user(user_create)
    return user


@pytest.fixture
def admin_token(client: TestClient, test_admin_user):
    """Get admin authentication token."""
    login_data = {
        "username": test_admin_user.email,
        "password": "SecurePass123",
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    response.raise_for_status()
    return response.json()["access_token"]


@pytest.fixture
def user_token(client: TestClient, test_user):
    """Get regular user authentication token."""
    login_data = {
        "username": test_user.email,
        "password": "SecurePass123",
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    response.raise_for_status()
    return response.json()["access_token"]


@pytest.fixture(scope="function")
def authenticated_client(client: TestClient, user_token) -> TestClient:
    """Create authenticated test client."""

    # Set authorization header
    client.headers.update({"Authorization": f"Bearer {user_token}"})

    return client


@pytest.fixture(scope="function")
def admin_client(client: TestClient, admin_token) -> TestClient:
    """Create authenticated test client with admin privileges."""

    # Set authorization header
    client.headers.update({"Authorization": f"Bearer {admin_token}"})

    return client


# Pytest markers for test categorization
pytest_plugins = [
    "pytest_asyncio",
]


def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests for individual components")
    config.addinivalue_line(
        "markers", "integration: Integration tests for component interaction"
    )
    config.addinivalue_line("markers", "api: API endpoint tests")
    config.addinivalue_line("markers", "database: Database operation tests")
    config.addinivalue_line("markers", "calculations: Engineering calculation tests")
    config.addinivalue_line("markers", "standards: Standards compliance tests")
    config.addinivalue_line("markers", "security: Security validation tests")
    config.addinivalue_line("markers", "performance: Performance benchmarking tests")
    config.addinivalue_line("markers", "slow: Tests that take longer than 1 second")


def pytest_collection_modifyitems(config, items):
    """Automatically mark tests based on their location."""
    for item in items:
        # Mark database tests
        if "database" in str(item.fspath) or "db_session" in item.fixturenames:
            item.add_marker(pytest.mark.database)

        # Mark repository tests
        if "repositories" in str(item.fspath):
            item.add_marker(pytest.mark.repository)
            item.add_marker(pytest.mark.database)
            item.add_marker(pytest.mark.unit)

        # Mark API tests
        if "api" in str(item.fspath):
            item.add_marker(pytest.mark.api)

        # Mark calculation tests
        if "calculations" in str(item.fspath):
            item.add_marker(pytest.mark.calculations)

        # Mark service tests
        if "services" in str(item.fspath):
            item.add_marker(pytest.mark.service)

        # Mark standards tests
        if "standards" in str(item.fspath):
            item.add_marker(pytest.mark.standards)

        # Mark integration tests
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)

        # Mark security tests
        if "security" in str(item.fspath):
            item.add_marker(pytest.mark.security)

        # Mark performance tests
        if "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)

        # Mark standards tests
        if "standards" in str(item.fspath):
            item.add_marker(pytest.mark.standards)
        else:
            # Default to unit tests
            item.add_marker(pytest.mark.unit)
