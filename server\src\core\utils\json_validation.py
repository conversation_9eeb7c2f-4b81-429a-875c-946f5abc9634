# src/core/utils/json_validation.py
"""JSON Validation Utilities.

This module provides utilities for validating structured JSON data stored
in database columns using Pydantic schemas.

Key Features:
- Pydantic-based JSON validation
- Custom SQLAlchemy type decorators
- JSON column validation helpers
- Error handling for malformed JSON
"""

import json
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Union

from pydantic import BaseModel, ValidationError
from sqlalchemy import Text, TypeDecorator

from src.config.logging_config import logger

# Lazy imports to avoid circular dependencies
if TYPE_CHECKING:
    from src.core.errors.unified_error_handler import handle_utility_errors
    from src.core.monitoring.unified_performance_monitor import (
        monitor_utility_performance,
    )


def _apply_utility_decorators(func: Any, operation_name: str) -> Any:
    """Apply utility decorators dynamically to avoid circular imports."""
    try:
        from src.core.errors.unified_error_handler import handle_utility_errors
        from src.core.monitoring.unified_performance_monitor import (
            monitor_utility_performance,
        )

        # Apply decorators
        decorated_func = handle_utility_errors(operation_name)(func)
        decorated_func = monitor_utility_performance(operation_name)(decorated_func)
        return decorated_func
    except ImportError:
        # If decorators can't be imported, return original function
        logger.warning(
            f"Could not apply decorators to {operation_name}, using original function"
        )
        return func


class JSONValidationError(Exception):
    """Exception raised when JSON validation fails."""

    def __init__(
        self, message: str, validation_errors: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.validation_errors = validation_errors
        super().__init__(message)


def _validate_json_data_impl(
    json_data: Union[str, Dict[str, Any], Any],
    schema_class: type[BaseModel],
    strict: bool = True,
) -> BaseModel:
    """Validate JSON data against a Pydantic schema.

    Args:
        json_data: JSON data to validate (string, dict, or object)
        schema_class: Pydantic schema class for validation
        strict: Whether to use strict validation

    Returns:
        BaseModel: Validated Pydantic model instance

    Raises:
        JSONValidationError: If validation fails

    """
    try:
        # Parse JSON string if needed
        if isinstance(json_data, str):
            try:
                parsed_data = json.loads(json_data)
            except json.JSONDecodeError as e:
                raise JSONValidationError(f"Invalid JSON format: {e}")
        else:
            parsed_data = json_data

        # Validate with Pydantic schema
        if hasattr(schema_class, "model_validate"):
            # Pydantic v2
            return schema_class.model_validate(parsed_data, strict=strict)
        # Pydantic v1
        return schema_class.parse_obj(parsed_data)

    except ValidationError as e:
        error_details = {}
        for error in e.errors():
            field_path = ".".join(str(loc) for loc in error["loc"])
            error_details[field_path] = error["msg"]

        raise JSONValidationError(
            f"JSON validation failed for schema {schema_class.__name__}",
            validation_errors=error_details,
        )
    except Exception as e:
        raise JSONValidationError(f"Unexpected error during JSON validation: {e}")


# Create decorated version lazily
_validate_json_data_decorated = None


def validate_json_data(
    json_data: Union[str, Dict[str, Any], Any],
    schema_class: type[BaseModel],
    strict: bool = True,
) -> Any:
    """Validate JSON data against a Pydantic schema.

    Args:
        json_data: JSON data to validate (string, dict, or any object)
        schema_class: Pydantic schema class for validation
        strict: Whether to use strict validation

    Returns:
        BaseModel: Validated Pydantic model instance

    Raises:
        JSONValidationError: If validation fails

    """
    global _validate_json_data_decorated
    if _validate_json_data_decorated is None:
        _validate_json_data_decorated = _apply_utility_decorators(
            _validate_json_data_impl, "validate_json_data"
        )
    return _validate_json_data_decorated(json_data, schema_class, strict)


def validate_json_string(
    json_string: str, schema_class: type[BaseModel], strict: bool = True
) -> str:
    """Validate JSON string and return normalized JSON string.

    Args:
        json_string: JSON string to validate
        schema_class: Pydantic schema class for validation
        strict: Whether to use strict validation

    Returns:
        str: Normalized JSON string

    Raises:
        JSONValidationError: If validation fails

    """
    validated_model = validate_json_data(json_string, schema_class, strict)

    # Convert back to JSON string
    if hasattr(validated_model, "model_dump_json"):
        # Pydantic v2
        return str(validated_model.model_dump_json())
    # Pydantic v1
    return str(validated_model.json())


def safe_json_loads(json_string: str, default: Any = None) -> Any:
    """Safely parse JSON string with fallback.

    Args:
        json_string: JSON string to parse
        default: Default value if parsing fails

    Returns:
        Parsed JSON data or default value

    """
    try:
        return json.loads(json_string)
    except (json.JSONDecodeError, TypeError):
        logger.warning(f"Failed to parse JSON: {json_string}")
        return default


def safe_json_dumps(data: Any, default: str = "null", **kwargs: Any) -> str:
    """Safely serialize data to JSON string.

    Args:
        data: Data to serialize
        default: Default string if serialization fails
        **kwargs: Additional arguments for json.dumps

    Returns:
        JSON string or default value

    """
    try:
        return json.dumps(data, **kwargs)
    except (TypeError, ValueError):
        logger.warning(f"Failed to serialize to JSON: {data}")
        return default


# SQLAlchemy Custom Types


class ValidatedJSON(TypeDecorator[str]):
    """SQLAlchemy type decorator for validated JSON columns.

    This type automatically validates JSON data against a Pydantic schema
    when storing and retrieving from the database.
    """

    impl = Text
    cache_ok = True

    def __init__(
        self, schema_class: type[BaseModel], *args: Any, **kwargs: Any
    ) -> None:
        self.schema_class = schema_class
        super().__init__(*args, **kwargs)

    def process_bind_param(self, value: Any, dialect: Any) -> Optional[str]:
        """Process value when storing to database."""
        if value is None:
            return None

        try:
            # Validate the data
            validated_model = validate_json_data(value, self.schema_class)

            # Convert to JSON string
            if hasattr(validated_model, "model_dump_json"):
                # Pydantic v2
                return str(validated_model.model_dump_json())
            # Pydantic v1
            return str(validated_model.json())

        except JSONValidationError as e:
            logger.error(f"JSON validation failed for column: {e.message}")
            raise

    def process_result_value(self, value: Any, dialect: Any) -> Any:
        """Process value when retrieving from database."""
        if value is None:
            return None

        try:
            # Parse and validate JSON
            return validate_json_data(value, self.schema_class)
        except JSONValidationError as e:
            logger.error(f"Failed to validate JSON from database: {e.message}")
            # Return raw data if validation fails (for backwards compatibility)
            return safe_json_loads(value, {})


class FlexibleJSON(TypeDecorator[str]):
    """SQLAlchemy type decorator for flexible JSON columns.

    This type stores JSON data without validation but provides
    safe parsing and serialization.
    """

    impl = Text
    cache_ok = True

    def process_bind_param(self, value: Any, dialect: Any) -> Optional[str]:
        """Process value when storing to database."""
        if value is None:
            return None

        return safe_json_dumps(value)

    def process_result_value(self, value: Any, dialect: Any) -> Any:
        """Process value when retrieving from database."""
        if value is None:
            return None

        return safe_json_loads(value, {})


# Helper functions for common validation patterns


def _validate_settings_json_impl(
    json_data: Union[str, Dict[str, Any]],
    required_keys: Optional[List[str]] = None,
    allowed_keys: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """Validate settings/configuration JSON data.

    Args:
        json_data: JSON data to validate
        required_keys: List of required keys
        allowed_keys: List of allowed keys (None for any)

    Returns:
        Dict: Validated settings dictionary

    Raises:
        JSONValidationError: If validation fails

    """
    # Parse JSON if string
    if isinstance(json_data, str):
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            raise JSONValidationError(f"Invalid JSON format: {e}")
    else:
        data = json_data

    if not isinstance(data, dict):
        raise JSONValidationError("Settings JSON must be an object/dictionary")

    # Check required keys
    if required_keys:
        missing_keys = set(required_keys) - set(data.keys())
        if missing_keys:
            raise JSONValidationError(f"Missing required keys: {missing_keys}")

    # Check allowed keys
    if allowed_keys:
        invalid_keys = set(data.keys()) - set(allowed_keys)
        if invalid_keys:
            raise JSONValidationError(f"Invalid keys: {invalid_keys}")

    return data


# Create decorated version lazily
_validate_settings_json_decorated = None


def validate_settings_json(
    json_data: Union[str, Dict[str, Any]],
    required_keys: Optional[List[str]] = None,
    allowed_keys: Optional[List[str]] = None,
) -> Any:
    """Validate settings JSON with key constraints.

    Args:
        json_data: JSON data to validate
        required_keys: List of required keys
        allowed_keys: List of allowed keys (if None, all keys allowed)

    Returns:
        Dict: Validated settings dictionary

    Raises:
        JSONValidationError: If validation fails

    """
    global _validate_settings_json_decorated
    if _validate_settings_json_decorated is None:
        _validate_settings_json_decorated = _apply_utility_decorators(
            _validate_settings_json_impl, "validate_settings_json"
        )
    return _validate_settings_json_decorated(json_data, required_keys, allowed_keys)


def validate_metadata_json(
    json_data: Union[str, Dict[str, Any]], max_depth: int = 5, max_keys: int = 100
) -> Dict[str, Any]:
    """Validate metadata JSON with size and depth limits.

    Args:
        json_data: JSON data to validate
        max_depth: Maximum nesting depth
        max_keys: Maximum number of keys (total)

    Returns:
        Dict: Validated metadata dictionary

    Raises:
        JSONValidationError: If validation fails

    """
    # Parse JSON if string
    if isinstance(json_data, str):
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            raise JSONValidationError(f"Invalid JSON format: {e}")
    else:
        data = json_data

    if not isinstance(data, dict):
        raise JSONValidationError("Metadata JSON must be an object/dictionary")

    # Check depth
    def check_depth(obj: Any, current_depth: int = 0) -> None:
        if current_depth > max_depth:
            raise JSONValidationError(f"JSON depth exceeds maximum of {max_depth}")

        if isinstance(obj, dict):
            for value in obj.values():
                check_depth(value, current_depth + 1)
        elif isinstance(obj, list):
            for item in obj:
                check_depth(item, current_depth + 1)

    check_depth(data)

    # Check total key count
    def count_keys(obj: Any) -> int:
        count = 0
        if isinstance(obj, dict):
            count += len(obj)
            for value in obj.values():
                count += count_keys(value)
        elif isinstance(obj, list):
            for item in obj:
                count += count_keys(item)
        return count

    total_keys = count_keys(data)
    if total_keys > max_keys:
        raise JSONValidationError(
            f"Total key count {total_keys} exceeds maximum of {max_keys}"
        )

    return data
