# tests/calculations/conftest.py - Fixtures for calculations layer tests
import pytest
from typing import Any


@pytest.fixture(scope="function")
def tank_heat_loss_test_data() -> dict[str, Any]:
    """Provide test data for tank heat loss calculations."""
    return {
        "vertical_cylinder": {
            "diameter_ft": 8.0,
            "height_ft": 15.0,
            "maintain_temp_f": 160.0,
            "ambient_temp_f": 70.0,
            "insulation_thickness_inches": 2.5,
            "insulation_material": "fiberglass",
            "is_outdoor": True,
            "wind_speed_mph": 15.0,
        },
        "horizontal_cylinder": {
            "diameter_ft": 6.0,
            "height_ft": 12.0,
            "maintain_temp_f": 150.0,
            "ambient_temp_f": 50.0,
            "insulation_thickness_inches": 1.5,
            "insulation_material": "fiberglass",
            "is_outdoor": False,
            "wind_speed_mph": 0.0,
        },
        "sphere": {
            "diameter_ft": 10.0,
            "height_ft": 10.0,  # Not used for sphere
            "maintain_temp_f": 180.0,
            "ambient_temp_f": 60.0,
            "insulation_thickness_inches": 3.0,
            "insulation_material": "mineral_wool",
            "is_outdoor": True,
            "wind_speed_mph": 20.0,
        },
    }


@pytest.fixture(scope="function")
def power_calculation_test_data() -> dict[str, Any]:
    """Provide test data for power calculations."""
    return {
        "electrical_power": {
            "voltage": 230.0,
            "current": 10.0,
            "resistance": 23.0,
            "power_factor": 0.85,
        },
        "heat_tracing_power": {
            "heat_loss_per_meter": 25.0,
            "length": 100.0,
            "safety_factor": 1.3,
            "efficiency": 0.9,
            "voltage": 230.0,
        },
        "three_phase_power": {
            "line_voltage": 400.0,
            "line_current": 15.0,
            "power_factor": 0.9,
            "phases": 3,
        },
    }


@pytest.fixture(scope="function")
def pipe_sizing_test_data() -> dict[str, Any]:
    """Provide test data for pipe sizing calculations."""
    return {
        "standard_sizes": [
            "1/2",
            "3/4",
            "1",
            "1-1/4",
            "1-1/2",
            "2",
            "3",
            "4",
            "6",
            "8",
        ],
        "nps_to_dn_mappings": {
            "1/2": 15,
            "3/4": 20,
            "1": 25,
            "2": 50,
            "3": 80,
            "4": 100,
            "6": 150,
            "8": 200,
        },
        "valve_allowances": {
            "light_flanged": {"1": 2.0, "2": 2.5, "3": 3.0, "4": 4.0},
            "heavy_flanged": {"1": 3.0, "2": 4.0, "3": 5.0, "4": 6.0},
        },
    }
