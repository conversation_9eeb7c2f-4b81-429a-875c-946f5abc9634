# Theming System Migration Guide

**Ultimate Electrical Designer - Client Application**

## Overview

This document outlines the complete redesign and migration of the theming system for Ultimate Electrical Designer, implementing engineering-grade standards with comprehensive TypeScript support, accessibility compliance, and seamless integration with the existing settings module.

## Migration Summary

### What Changed

1. **Complete Theme Provider Redesign**: Replaced manual CSS media queries with `next-themes` based provider
2. **New Theme Toggle Components**: Created accessible, keyboard-navigable theme toggle components
3. **Settings Integration**: Enhanced settings module with custom theme field component
4. **SSR-Safe Implementation**: Proper hydration handling to prevent mismatches
5. **WCAG 2.1 AA Compliance**: Full accessibility support with screen reader compatibility

### Breaking Changes

⚠️ **IMPORTANT**: This is a breaking change migration with no backward compatibility.

- **Removed**: Manual CSS media query theme detection
- **Removed**: Static theme class applications
- **Changed**: Theme persistence mechanism (now uses `next-themes` storage)
- **Changed**: Theme context API (new hooks and providers)

## New Architecture

### 1. Theme Provider (`UEDThemeProvider`)

**Location**: `client/src/components/theme-provider.tsx`

```typescript
import { UEDThemeProvider } from '@/components/theme-provider'

// In your layout
<UEDThemeProvider
  themes={['light', 'dark', 'system']}
  defaultTheme="system"
  enableSystem={true}
  storageKey="ued-theme"
>
  {children}
</UEDThemeProvider>
```

**Features**:

- TypeScript interfaces matching settings module types
- Error boundaries with fallback handling
- SSR-safe theme detection
- Custom theme configurations
- Accessibility metadata support

### 2. Theme Toggle Components

**Location**: `client/src/components/ui/theme-toggle.tsx`

#### Available Components

```typescript
// Main theme toggle with full customization
<ThemeToggle
  variant="outline"
  size="default"
  showLabel={true}
  showDescriptions={true}
/>

// Icon-only version
<ThemeToggleIcon />

// With label version
<ThemeToggleWithLabel label="Change Theme" />

// Compact version for navigation
<ThemeToggleCompact />
```

**Features**:

- Keyboard accessibility (WCAG 2.1 AA)
- Visual indicators for current theme
- Dropdown interface with descriptions
- Multiple size and variant options
- Screen reader support

### 3. Settings Integration

**Location**: `client/src/modules/settings/components/ThemeField.tsx`

The settings module now includes a custom theme field that integrates seamlessly with the new theming system:

```typescript
// Automatically used in PreferencesForm for theme fields
<ThemeField
  field={themeField}
  currentValue={currentTheme}
  hasError={false}
  onFieldChange={handleThemeChange}
/>
```

**Features**:

- Real-time theme preview
- Cross-tab synchronization
- User preferences API integration
- Loading states and error handling

## Implementation Details

### Layout Integration

**File**: `client/src/app/layout.tsx`

```typescript
export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en" className="h-full scroll-smooth" suppressHydrationWarning>
      <body className={`${inter.className} h-full antialiased`}>
        <UEDThemeProvider
          themes={['light', 'dark', 'system']}
          defaultTheme="system"
          enableSystem={true}
          storageKey="ued-theme"
          disableTransitionOnChange={false}
        >
          <ReactQueryProvider>
            <div className="h-full">{children}</div>
          </ReactQueryProvider>
        </UEDThemeProvider>
      </body>
    </html>
  )
}
```

### CSS Variables Integration

The new system automatically applies theme-specific CSS custom properties:

```css
:root {
  --color-primary: /* theme-specific value */;
  --color-secondary: /* theme-specific value */;
  /* ... other theme variables */
}

[data-theme='dark'] {
  --color-primary: /* dark theme value */;
  --color-secondary: /* dark theme value */;
  /* ... other dark theme variables */
}
```

### Theme Configuration

**File**: `client/src/modules/settings/constants.ts`

```typescript
export const THEME_CONFIGS: Record<string, ThemeConfig> = {
  light: {
    name: 'light',
    label: 'Light',
    colors: {
      primary: 'oklch(0.21 0.006 285.885)',
      secondary: 'oklch(0.967 0.001 286.375)',
      background: 'oklch(1 0 0)',
      foreground: 'oklch(0.141 0.005 285.823)',
      // ... other colors
    },
  },
  dark: {
    name: 'dark',
    label: 'Dark',
    colors: {
      primary: 'oklch(0.985 0 0)',
      secondary: 'oklch(0.141 0.005 285.823)',
      background: 'oklch(0.141 0.005 285.823)',
      foreground: 'oklch(0.985 0 0)',
      // ... other colors
    },
  },
}
```

## Usage Examples

### Basic Theme Toggle

```typescript
import { ThemeToggle } from '@/components/ui/theme-toggle'

function Header() {
  return (
    <header>
      <nav>
        {/* Other nav items */}
        <ThemeToggle variant="ghost" size="sm" />
      </nav>
    </header>
  )
}
```

### Custom Theme Hook

```typescript
import { useUEDTheme } from '@/components/theme-provider'

function MyComponent() {
  const { theme, setTheme, resolvedTheme, mounted } = useUEDTheme()

  if (!mounted) return <div>Loading...</div>

  return (
    <div>
      <p>Current theme: {theme}</p>
      <p>Resolved theme: {resolvedTheme}</p>
      <button onClick={() => setTheme('dark')}>
        Switch to Dark
      </button>
    </div>
  )
}
```

### Settings Integration

The theme field is automatically integrated into the settings form when the field ID is 'theme' and type is 'select'.

## Testing

### Unit Tests

- **Theme Provider**: `client/src/components/__tests__/theme-provider.test.tsx`
- **Theme Toggle**: `client/src/components/ui/__tests__/theme-toggle.test.tsx`
- **Theme Field**: `client/src/modules/settings/components/__tests__/ThemeField.test.tsx`

### E2E Tests

- **Complete Flow**: `client/tests/e2e/theming.spec.ts`

### Running Tests

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Coverage
npm run test:coverage
```

## Accessibility Features

### WCAG 2.1 AA Compliance

- **Keyboard Navigation**: Full keyboard support for all theme controls
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Focus Management**: Visible focus indicators and logical tab order
- **Color Contrast**: Maintains proper contrast ratios in all themes
- **Reduced Motion**: Respects user's motion preferences

### Accessibility Testing

```typescript
// Example accessibility test
import { axe } from 'jest-axe'

test('theme toggle is accessible', async () => {
  const { container } = render(<ThemeToggle />)
  const results = await axe(container)
  expect(results).toHaveNoViolations()
})
```

## Performance Considerations

### SSR Safety

- Prevents hydration mismatches with proper mounting detection
- Uses `suppressHydrationWarning` on HTML element
- Graceful loading states during theme resolution

### Bundle Size

- Tree-shakeable components
- Minimal runtime overhead
- Efficient theme switching without page reloads

## Troubleshooting

### Common Issues

1. **Hydration Mismatch**: Ensure `suppressHydrationWarning` is set on HTML element
2. **Theme Not Persisting**: Check localStorage permissions and storage key
3. **CSS Variables Not Applied**: Verify theme configuration and CSS custom properties

### Debug Mode

```typescript
// Enable debug logging
<UEDThemeProvider
  themes={['light', 'dark', 'system']}
  defaultTheme="system"
  // Add debug prop if available
>
  {children}
</UEDThemeProvider>
```

## Future Enhancements

### Planned Features

1. **Custom Theme Builder**: UI for creating custom themes
2. **Theme Animations**: Smooth transitions between themes
3. **High Contrast Mode**: Additional accessibility theme
4. **Theme Scheduling**: Automatic theme switching based on time

### Extension Points

The theming system is designed to be extensible:

- Custom theme configurations
- Additional theme variants
- Plugin-based theme extensions
- Integration with design tokens

## Support

For questions or issues related to the theming system:

1. Check this documentation
2. Review the test files for usage examples
3. Consult the TypeScript interfaces for API details
4. Refer to the settings module integration patterns

---

**Last Updated**: 2025-07-18  
**Version**: 1.0.0  
**Compatibility**: Ultimate Electrical Designer Client v1.0+
