# Ultimate Electrical Designer - Development Makefile
# Type Safety and Development Workflow Commands

# Define PHONY targets to ensure commands are run even if files with the same name exist
.PHONY: help install dev-setup clean
.PHONY: type-check type-check-critical type-check-full type-check-client
.PHONY: lint format check-format-server lint-client format-client check-format-client security-check
.PHONY: test test-unit test-integration test-server test-client test-client-failing test-coverage
.PHONY: pre-commit-install pre-commit-run
.PHONY: dev start-client start-server start-cad-integrator start-computation-engine start-all
.PHONY: db-migrate db-reset
.PHONY: docs-build
.PHONY: ci-check

# Default target
help:
	@echo "🔧 Ultimate Electrical Designer - Development Commands"
	@echo "=================================================="
	@echo ""
	@echo "📦 Setup Commands:"
	@echo "  install                  Install all dependencies (Python & Node.js)"
	@echo "  dev-setup                Complete development environment setup"
	@echo ""
	@echo "🚀 Development Servers:"
	@echo "  start-all                Start client, server, CAD Integrator, and Computation Engine"
	@echo "  start-client             Start Next.js frontend development server"
	@echo "  start-server             Start FastAPI backend development server"
	@echo "  start-cad-integrator     Start C# CAD Integrator service (via Docker Compose)"
	@echo "  start-computation-engine Start C# Computation Engine service (via Docker Compose)"
	@echo ""
	@echo "🔍 Type Safety Commands:"
	@echo "  type-check               Run comprehensive Python type safety validation (via script)"
	@echo "  type-check-critical      Run critical Python modules type checking only (direct mypy)"
	@echo "  type-check-full          Run full Python type checking (may fail due to SQLAlchemy)"
	@echo "  type-check-client        Run TypeScript type checking for the client"
	@echo ""
	@echo "🧪 Testing Commands:"
	@echo "  test-server              Run all Python backend tests"
	@echo "  test-unit                Run Python unit tests only"
	@echo "  test-integration         Run Python integration tests only"
	@echo "  test-coverage            Generate Python backend test coverage report"
	@echo "  test-client-ui           Run all Next.js frontend tests with Vitest UI reporting"
	@echo "  test-client              Run all Next.js frontend tests"
	@echo "  test-e2e                 Run all Playwright E2E tests"
	@echo "  test-client-failing      Generate report for failing Next.js frontend tests"
	@echo ""
	@echo "🎨 Code Quality Commands:"
	@echo "  lint-server              Run all Python linting checks (ruff)"
	@echo "  format-server            Format Python code with ruff"
	@echo "  check-format-server      Check Python code formatting (for CI/pre-commit)"
	@echo "  lint-client              Run Next.js frontend linting checks"
	@echo "  format-client            Format Next.js frontend code"
	@echo "  check-format-client      Check Next.js frontend code formatting (for CI/pre-commit)"
	@echo "  security-check-server    Run Python security scanning with bandit"
	@echo ""
	@echo "🗄️  Database Commands:"
	@echo "  db-migrate               Apply database migrations to head"
	@echo "  db-reset                 Reset and re-migrate the database (DANGER!)"
	@echo ""
	@echo "🧹 Utility Commands:"
	@echo "  clean                    Clean up temporary files and caches"
	@echo "  pre-commit-install       Install pre-commit hooks for Python (server) and Node.js (client)"
	@echo "  pre-commit-run           Manually run pre-commit hooks on all files"
	@echo ""
	@echo "📚 Documentation Commands:"
	@echo "  docs-build               Build Python API documentation (mkdocs)"
	@echo ""
	@echo "⚙️  CI/CD Simulation:"
	@echo "  ci-check                 Run all CI/CD pre-merge checks"


#
# Installation and setup
#

# Installs Python dependencies (via Poetry) and Node.js dependencies (via npm)
install:
	@echo "📦 Installing Python dependencies in server/ (using Poetry)..."
	cd server && poetry install --sync
	@echo "📦 Installing Node.js dependencies in client/ (using npm)..."
	cd client && npm install
	@echo "📦 Initializing pre-commit for Python server (if not already done)..."
	cd server && poetry run pre-commit install --install-hooks

# Complete development environment setup: install dependencies and hooks
dev-setup: install pre-commit-install
	@echo "🔧 Setting up development environment..."
	@echo "✅ Development environment ready!"

#
# Development Servers
#

# Start Next.js frontend development server
start-client:
	@echo "🚀 Starting Next.js frontend development server..."
	cd client && npm run dev

# Start FastAPI backend development server (using Poetry for uvicorn)
start-server:
	@echo "🚀 Starting FastAPI backend development server..."
	cd server && poetry run uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# Start C# CAD Integrator service using Docker Compose
start-cad-integrator:
	@echo "🚀 Starting C# CAD Integrator service via Docker Compose..."
	# Ensure docker-compose.yml defines cad-integrator-service
	docker-compose up -d cad-integrator-service

# Start C# Computation Engine service using Docker Compose
start-computation-engine:
	@echo "🚀 Starting C# Computation Engine service via Docker Compose..."
	# Ensure docker-compose.yml defines computation-engine-service
	docker-compose up -d computation-engine-service

# Start all development services
start-all: start-client start-server start-cad-integrator start-computation-engine
	@echo "🎉 All development services are starting up!"
	@echo "Client: http://localhost:3000"
	@echo "Server: http://localhost:8000"
	@echo "CAD Integrator Service & Computation Engine Service: Check your Docker logs (docker logs <container_name>)"

# Alias for start-server for convenience
dev: start-server

#
# Type safety validation
#

# Run comprehensive Python type safety validation via external script
# Uses configuration from pyproject.toml -> [tool.mypy]
type-check:
	@echo "🔍 Running comprehensive Python type safety validation..."
	cd server && ./scripts/type_safety_check.sh
	@echo "✅ Comprehensive Python type checking completed."

# Run critical Python modules type checking only (direct mypy calls)
type-check-critical:
	@echo "🎯 Running critical Python modules type checking..."
	cd server && poetry run mypy src/core/utils/performance_optimizer.py --show-error-codes --ignore-missing-imports
	cd server && poetry run mypy src/core/utils/memory_manager.py --show-error-codes --ignore-missing-imports
	cd server && poetry run mypy src/core/utils/json_validation.py --show-error-codes --ignore-missing-imports
	cd server && poetry run mypy src/core/utils/file_io_utils.py --show-error-codes --ignore-missing-imports
	cd server && poetry run mypy src/config/settings.py --show-error-codes --ignore-missing-imports
	@echo "✅ Critical modules Python type checking completed"

# Run full Python type checking (may fail due to SQLAlchemy)
type-check-full:
	@echo "⚠️  Running full Python type checking (may fail due to SQLAlchemy issue)..."
	cd server && poetry run mypy src/ --show-error-codes || echo "❌ Full Python type checking might be affected by SQLAlchemy compatibility."

# Run TypeScript type checking for the client
type-check-client:
	@echo "🔍 Running TypeScript type checking for the client..."
	cd client && npm run type-check
	@echo "✅ Client TypeScript type checking completed."

#
# Testing
#

# Run all Python backend tests
# Uses configuration from pyproject.toml -> [tool.pytest.ini_options]
test-server:
	@echo "🧪 Running all Python backend tests..."
	cd server && poetry run pytest tests/ -v

# Run Python unit tests only (uses pytest markers defined in pyproject.toml)
test-server-unit:
	@echo "🧪 Running Python unit tests..."
	cd server && poetry run pytest -v -m "not integration and not performance" tests/

# Run Python integration tests only (uses pytest markers)
test-server-integration:
	@echo "🧪 Running Python integration tests..."
	cd server && poetry run pytest -v -m integration tests/

# Generate Python backend test coverage report
# Uses configuration from pyproject.toml -> [tool.coverage.*]
test-server-coverage:
	@echo "📊 Generating Python backend test coverage report..."
	cd server && poetry run pytest --cov=src --cov-report=term-missing --cov-report=xml --cov-report=html tests/
	@echo "✅ Python test coverage report generated (see server/htmlcov/index.html and server/coverage.xml)"

# Run all Next.js frontend tests
test-client:
	@echo "🧪 Running all Next.js frontend tests..."
	cd client && npm run test

# Run all Next.js frontend tests with ui
test-client-ui:
	@echo "🧪 Running all Next.js frontend tests with ui..."
	cd client && npm run test:ui

# Run all Playwright E2E tests
test-e2e:
	@echo "🧪 Running all Playwright E2E tests..."
	cd client && npm run test:e2e

# Generate report for failing Next.js frontend tests
test-client-failing:
	@echo "🧪 Generating report for failing Next.js frontend tests..."
	cd client && npm run test:failing

#
# Code quality
#

# Run Python type checking (mypy)
type-check-server:
	@echo "🔍 Running Python type checking (mypy)..."
	cd server && poetry run mypy src/ --show-error-codes

# Run Python linting checks (ruff)
lint-server:
	@echo "🔍 Running Python linting checks (ruff)..."
	cd server && poetry run ruff check .

# Format Python code with ruff
format-server:
	@echo "🎨 Formatting Python code (ruff)..."
	cd server && poetry run ruff format .
	@echo "✅ Python code formatting completed."

# Check Python code formatting (for CI/pre-commit)
check-format-server:
	@echo "🔎 Checking Python code formatting (ruff)..."
	cd server && poetry run ruff format . --check
	@echo "✅ Python code formatting check completed."

# Run Next.js frontend type checking
lint-client-type-check:
	@echo "🔍 Running Next.js frontend type checking..."
	cd client && npm run type-check

# Run Next.js frontend linting checks
lint-client:
	@echo "🔍 Running Next.js frontend linting checks..."
	cd client && npm run lint

# Format Next.js frontend code
format-client:
	@echo "🎨 Formatting Next.js frontend code..."
	cd client && npm run format
	@echo "✅ Next.js frontend code formatting completed."

# Check Next.js frontend code formatting (for CI/pre-commit)
check-format-client:
	@echo "🔎 Checking Next.js frontend code formatting..."
	cd client && npm run check:format
	@echo "✅ Next.js frontend code formatting check completed."

# Run Python security scanning with bandit
security-check-server:
	@echo "🔒 Running Python security checks (bandit)..."
	cd server && poetry run bandit -r src/ -f json -o bandit-report.json
	@echo "✅ Python security check completed - see server/bandit-report.json"

#
# Pre-commit hooks
#

# Install pre-commit hooks for Python (server) and Node.js (client)
pre-commit-install:
	@echo "🪝 Installing pre-commit hooks for server (Python) and client (Node.js)..."
	cd server && poetry run pre-commit install --install-hooks
	cd client && npm run postinstall # This should trigger husky install for the client
	@echo "✅ Pre-commit hooks installed."

# Manually run pre-commit hooks on all files
pre-commit-run:
	@echo "🪝 Running pre-commit hooks on all files..."
	cd server && poetry run pre-commit run --all-files
	cd client && npx lint-staged --allow-empty # lint-staged mimics pre-commit context for client
	@echo "✅ Pre-commit hooks run completed."

#
# Cleanup
#

# Clean up temporary files and caches across the monorepo
clean:
	@echo "🧹 Cleaning up temporary files and caches..."
	# Python backend cleanup
	find server -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find server -type f -name "*.pyc" -delete 2>/dev/null || true
	find server -type f -name "*.pyo" -delete 2>/dev/null || true
	find server -type f -name "*.pyd" -delete 2>/dev/null || true
	find server -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find server -type d -name ".ruff_cache" -exec rm -rf {} + 2>/dev/null || true
	find server -type d -name ".mypy_cache" -exec rm -rf {} + 2>/dev/null || true
	rm -f server/type_safety_report.txt 2>/dev/null || true
	rm -f server/bandit-report.json 2>/dev/null || true
	rm -f server/.coverage 2>/dev/null || true
	rm -rf server/htmlcov 2>/dev/null || true
	rm -f server/coverage.xml 2>/dev/null || true

	# Client frontend cleanup
	rm -rf client/.next client/node_modules client/coverage client/failing-vitest-tests.json 2>/dev/null || true
	rm -rf client/build 2>/dev/null || true # For Next.js 'build' output

	# General project cleanup (e.g., venv/env directories if outside server/)
	find . -type d -name ".venv" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "venv" -exec rm -rf {} + 2>/dev/null || true

	@echo "✅ Cleanup completed"


#
# Database operations
#

# Apply database migrations to head (using Alembic via Poetry)
db-migrate:
	@echo "🗄️  Running database migrations..."
	cd server && poetry run alembic upgrade head
	@echo "✅ Database migrations applied."

# Reset and re-migrate the database (DANGER!)
db-reset:
	@echo "🗄️  Resetting database..."
	@echo "⚠️  WARNING: This will DELETE your development database and re-migrate."
	@echo "   Press Ctrl+C to cancel in 5 seconds, or wait to proceed."
	@sleep 5
	cd server && rm -f src/database/app_dev.db # Adjust this path to your actual DB file location if different
	cd server && poetry run alembic upgrade head
	@echo "✅ Database reset and re-migrated."

#
# Documentation
#

# Build Python API documentation (using mkdocs via Poetry)
docs-build:
	@echo "📚 Building Python API documentation (mkdocs)..."
	# Assumes mkdocs.yml is in the server/ directory or a docs/ sub-directory within server/
	cd server && poetry run mkdocs build --clean --site-dir ../docs/api/python
	@echo "✅ Python documentation built. Output to server/docs/api/python"

#
# CI/CD simulation
#

# Run all CI/CD pre-merge checks
ci-check: type-check lint test check-format-server check-format-client security-check
	@echo "✅ CI/CD checks completed successfully."